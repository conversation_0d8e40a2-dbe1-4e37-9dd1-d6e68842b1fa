import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';

part 'short_details_state.freezed.dart';

@freezed
class ShortDetailsState extends BaseState with _$ShortDetailsState {
  const factory ShortDetailsState({
    @Default(ShortsDetailOneShotEvent.none)
    ShortsDetailOneShotEvent oneShotEvent,
    @Default(false) bool didShareOrDownloadShortsResult,
    @Default(false) bool isWatermarkEnable,
    @Default('') String selectedUrlShortsBackgrounds,
    @Default('') String currentPlayingFilePath,
    @Default('') String audioFilePath,
    @Default('') String subtitleFilePath,
    @Default(ProcessingStatus.initial) ProcessingStatus processingStatus,
    @Default(ShortQuizOneShotEvent.initial) ShortQuizOneShotEvent shortQuizOneShotEvent,
    @Default(<String, String>{}) Map<String, String> mergedVideosMap,
    @Default('') String quizVideoUrl,
    @Default('') String progressMessage,
    @Default('') String selectedQuizVideoUrl,
    @Default(false) bool isQuizControlsVisible,
  }) = _ShortDetailsState;
}

enum ShortsDetailOneShotEvent {
  none,
  showError,
  videoProcessingComplete,
  videoSaved,
}

enum ProcessingStatus {
  initial,
  apiGenerating,
  downloading,
  processing,
  complete,
  error,
}

enum ShortQuizOneShotEvent { initial, loading, success, error }
