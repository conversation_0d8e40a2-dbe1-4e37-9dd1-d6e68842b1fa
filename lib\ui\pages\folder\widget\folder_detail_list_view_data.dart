import 'package:note_x/lib.dart';

enum FolderDetailItemType {
  folder,
  note,
}

class FolderDetailItemData {
  final FolderDetailItemType type;
  final String id;
  final dynamic data; // Either FolderModel or NoteModel
  final int originalIndex; // Index in the original list (folders or notes)

  FolderDetailItemData({
  required this.type,
  required this.id,
  required this.data,
  required this.originalIndex,
  });

  bool get isFolder => type == FolderDetailItemType.folder;
  bool get isNote => type == FolderDetailItemType.note;

  FolderModel get folderData => data as FolderModel;
  NoteModel get noteData => data as NoteModel;
}

class FolderDetailListViewData {
  final List<FolderModel> folders;
  final List<NoteModel> notes;
  final List<FolderDetailItemData> _combinedItems;

  FolderDetailListViewData({
  required this.folders,
  required this.notes,
  }) : _combinedItems = _buildCombinedItems(folders, notes);

  static List<FolderDetailItemData> _buildCombinedItems(
  List<FolderModel> folders,
  List<NoteModel> notes,
  ) {
  final List<FolderDetailItemData> items = [];

  // Add folders first
  for (int i = 0; i < folders.length; i++) {
    items.add(FolderDetailItemData(
    type: FolderDetailItemType.folder,
    id: folders[i].id,
    data: folders[i],
    originalIndex: i,
    ));
  }

  // Add notes after folders
  for (int i = 0; i < notes.length; i++) {
    items.add(FolderDetailItemData(
    type: FolderDetailItemType.note,
    id: notes[i].id,
    data: notes[i],
    originalIndex: i,
    ));
  }

  return items;
  }

  List<FolderDetailItemData> get items => _combinedItems;
  int get totalCount => _combinedItems.length;
  bool get isEmpty => _combinedItems.isEmpty;
  bool get isNotEmpty => _combinedItems.isNotEmpty;

  List<String> get allItemIds => _combinedItems.map((item) => item.id).toList();
  List<String> get folderIds => folders.map((folder) => folder.id).toList();
  List<String> get noteIds => notes.map((note) => note.id).toList();

  FolderDetailItemData? getItemById(String id) {
  try {
    return _combinedItems.firstWhere((item) => item.id == id);
  } catch (e) {
    return null;
  }
  }

  FolderDetailItemData getItemAt(int index) {
  return _combinedItems[index];
  }
}