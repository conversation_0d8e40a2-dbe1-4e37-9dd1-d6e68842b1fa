import 'dart:async';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
// ignore: depend_on_referenced_packages
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'home_state.dart';

class HomeCubit extends BaseCubit<HomeState> {
  HomeCubit(super.initialState);

  final GetNoteApiServiceImpl _getNoteApiService =
      GetIt.instance.get<GetNoteApiServiceImpl>();
  final _folderService = GetIt.instance.get<FolderService>();
  final deleteNoteApiService = GetIt.instance.get<CreateNoteApiServiceImpl>();

  late TabController tabBarController;
  late PageController pageController;

  List<NoteModel> _communityNotes = [];
  List<NoteModel> _notesFailed = [];

  List<NoteModel> get getNotesFailed => _notesFailed;

  List<NoteModel> get getCommunityNotes => _communityNotes;

  int indexPageView = 0;
  StreamSubscription? _notesSubscription;
  SharedMediaFile? _sharedMedia;
  NoteType? _sharedNoteType;
  String? _sharedContent;
  ValueNotifier<bool> refreshCreateNoteOnValueChange = ValueNotifier(false);

  SharedMediaFile? get getSharedMedia => _sharedMedia;

  NoteType? get getSharedNoteType => _sharedNoteType;

  String? get getSharedContent => _sharedContent;
  bool _isPageControllerReady = false;
  final MultiSelectController _multiSelectController = MultiSelectController();
  MultiSelectController get multiSelectController => _multiSelectController;
  final GlobalKey<AnimatedBottomNavBarState> _bottomNavKey =
      GlobalKey<AnimatedBottomNavBarState>();
  GlobalKey<AnimatedBottomNavBarState> get bottomNavKey => _bottomNavKey;

  @override
  Future<void> close() {
    tabBarController.dispose();
    pageController.dispose();
    disposeAllNotesDataListener();
    refreshCreateNoteOnValueChange.dispose();
    return super.close();
  }

  void setSharedNoteType(NoteType? noteType) {
    _sharedNoteType = noteType;
  }

  void setSharedContent(String? content) {
    _sharedContent = content;
  }

  void setSharedMedia(SharedMediaFile? media) {
    _sharedMedia = media;
  }

  void initSharing(BuildContext context) {
    final shareService = ExternalShareService();
    shareService.onShareReceived = (media, type, content) {
      bool shouldUpdate = _sharedMedia?.path != media?.path ||
          _sharedNoteType != type ||
          _sharedContent != content;

      if (shouldUpdate) {
        _sharedMedia = media;
        _sharedNoteType = type;
        _sharedContent = content;

        if (context.mounted) {
          Navigator.of(context).popUntil((route) {
            final isFirst = route.isFirst;
            return isFirst;
          });
        }

        if (type == NoteType.youtube) {
          emit(state.copyWith(sharingTypeState: SharingTypeState.typeYoutube));
          Navigator.of(context).push(
            CupertinoPageRoute(
              builder: (context) => YoutubePage(
                content: content,
                onClose: () {
                  setSharedNoteType(null);
                  setSharedContent(null);
                  setSharedMedia(null);
                },
                onCreateNoteSuccess: () {
                  safeNavigateToPage(0);
                },
              ),
            ),
          );
        } else if (type == NoteType.document) {
          Navigator.of(context).push(
            CupertinoPageRoute(
              builder: (context) => UploadDocumentPage(
                sharedMediaFile: media,
                onClose: () {
                  setSharedNoteType(null);
                  setSharedContent(null);
                  setSharedMedia(null);
                },
                onCreateNoteSuccess: () {
                  safeNavigateToPage(0);
                },
              ),
            ),
          );
        } else {
          Navigator.of(context).push(
            CupertinoPageRoute(
              builder: (context) => UploadAudioPage(
                sharedMediaFile: media,
                onClose: () {
                  setSharedNoteType(null);
                  setSharedContent(null);
                  setSharedMedia(null);
                },
                onCreateNoteSuccess: () {
                  safeNavigateToPage(0);
                },
              ),
            ),
          );
        }

        refreshCreateNoteOnValueChange.value =
            !refreshCreateNoteOnValueChange.value;
        ReceiveSharingIntent.instance.reset();
      }
    };
  }

  void handleWidgetApp(BuildContext context) {
    final widgetApp = WidgetAppDeepLink();
    widgetApp.onDeepLinkReceived = (type) {
      Navigator.of(context).popUntil((route) => route.isFirst);
      switch (type) {
        case WidgetAppDeepLinkType.record:
          bool isCurrentlyOnRecordPage = false;
          Navigator.popUntil(context, (route) {
            isCurrentlyOnRecordPage =
                route.settings.name == RecordAppPage.routeName;
            return true;
          });

          if (!isCurrentlyOnRecordPage) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const RecordAppPage(isAutoStart: true),
                settings: const RouteSettings(name: RecordAppPage.routeName),
              ),
            );
          }
          break;

        case WidgetAppDeepLinkType.text:
          Navigator.of(context).push(
            CupertinoPageRoute(
              builder: (context) => UploadTextPage(
                onClose: () {
                  setSharedNoteType(null);
                  setSharedContent(null);
                  setSharedMedia(null);
                },
                onCreateNoteSuccess: () {
                  safeNavigateToPage(0);
                },
              ),
            ),
          );
          break;
        case WidgetAppDeepLinkType.youtube:
          Navigator.of(context).push(
            CupertinoPageRoute(
              builder: (context) => YoutubePage(
                onClose: () {
                  setSharedNoteType(null);
                  setSharedContent(null);
                  setSharedMedia(null);
                },
                onCreateNoteSuccess: () {
                  safeNavigateToPage(0);
                },
              ),
            ),
          );
          break;
        default:
          break;
      }
    };
  }

  void checkPendingShareAfterLogin() {
    final shareService = ExternalShareService();
    shareService.handleLoginSuccess();
  }

  void handleWidgetAppInit() {
    final widgetApp = WidgetAppDeepLink();
    widgetApp.handleWidgetAppInit();
    widgetApp.resetDeeplink();
  }

  void initAllNotesDataListener() {
    _notesSubscription?.cancel();

    _notesSubscription = HiveService().noteBox.watch().listen((_) {
      if (!isClosed) {
        applyFilterAndSort();
      }
    });
  }

  void disposeAllNotesDataListener() {
    _notesSubscription?.cancel();
    _notesSubscription = null;
  }

  void getCommunityNote() async {
    emit(state.copyWith(
        homePageCommunityNoteViewState:
            HomePageCommunityNoteViewState.getCommunityNoteLoading));
    try {
      final data = await _getNoteApiService.getCommunityNote();
      _communityNotes = data;
      _communityNotes.removeAt(1);
      emit(state.copyWith(
          homePageCommunityNoteViewState:
              HomePageCommunityNoteViewState.getCommunityNoteSuccess));
    } catch (err) {
      emit(state.copyWith(
          homePageCommunityNoteViewState:
              HomePageCommunityNoteViewState.getCommunityNoteError));
    }
  }

  void getAllFolders() async {
    try {
      await _folderService.getAllFolders();
      await getNoteFail();
    } catch (err) {
      await getNoteFail();
    }
  }

  Future<bool> deleteNote(String noteId) async {
    bool result = false;
    await runCubitCatching(action: () async {
      // Get note from Hive first to get backendNoteId
      final note = HiveService().noteBox.get(noteId);
      if (note == null) {
        throw AppErrorException(
            'Something went wrong! Please try again!', 'other');
      }

      // Delete from backend using backendNoteId
      await deleteNoteApiService.deleteNote(noteId: note.backendNoteId);
      result = true;

      // Update UI after delete success
      if (result) {
        // Delete note from Hive box
        await HiveService().noteBox.delete(noteId);

        // Update filtered notes by removing the deleted note
        final updatedNotes =
            state.filteredNotes.where((note) => note.id != noteId).toList();
        emit(state.copyWith(filteredNotes: updatedNotes));
      }
    });
    return result;
  }

  Future<void> getNoteFail() async {
    final userId = appCubit.getAppUser().id;
    final notes = await HiveService().getNotesFailed(userId);
    _notesFailed = notes;
  }

  void resetState() {
    emit(state.copyWith(
        homePageAllNoteViewState: HomePageAllNoteViewState.initial));
  }

  /// This function call when first fetch data on home
  /// And call on pull refresh, and call on delete note success
  /// And call if retry success note fail in note fail folder
  /// Will not call if create note success

  Future<void> syncAllNotes(bool isFirstOpen) async {
    /// Check hive box noteV2 to get all note status current is fail or loading
    //if box contains note fail or creating, we move them to box fail and notice user
    //then we call api to get all note, if api success we update list note, else error do we show empty list?
    //we need to distingush between sync note when app is pull refresh and update delete, or is from first fetch data
    //when open home
    //if is first open home, we move all note fail or loading to box fail, else we only move note fail to box fail.
    //if it is pull refresh, we need to keep notes which are in loading state, we just sync other notes from api list.
    final startTime = DateTime.now();
    emit(state.copyWith(isSyncing: true));
    final successNotes = HiveService().noteBox.values.where(
          (note) => note.noteStatus != NoteStatus.loading,
        );

    //call function to add upper fail notes to box fail (Hung implementing)
    await HiveService().moveErrorNotesToFailedBox();
    //now we call api v1/user/notes to get all notes from api
    //and save to note box v2, this will update list view on home tab 'All notes'
    try {
      getAllFolders();
      final data = await _getNoteApiService.getAllNotesV2();
      if (isFirstOpen) {
        await HiveService().noteBox.clear();
      } else {
        await HiveService().noteBox.deleteAll(successNotes.map((e) => e.id));
      }
      await HiveService().createNoteByAddAll(data
          .map((note) => note.copyWith(
              noteStatus: NoteStatus.success,
              folderName: HiveService()
                  .folderBox
                  .values
                  .firstWhereOrNull(
                      (folder) => folder.backendId == note.folderId)
                  ?.folderName))
          .toList());
    } catch (err) {
      emit(state.copyWith(isSyncing: false));
    } finally {
      final elapsedTime = DateTime.now().difference(startTime);
      const minimumDuration = Duration(milliseconds: 500);
      if (elapsedTime < minimumDuration) {
        await Future.delayed(minimumDuration);
      }
      emit(state.copyWith(isSyncing: false));
    }
  }

  Future<void> refreshAllNotes() async {
    try {
      final localNotes = HiveService()
          .noteBox
          .values
          .where((note) => note.noteStatus != NoteStatus.loading)
          .toList();
      await HiveService().moveErrorNotesToFailedBox();
      // get all notes from api
      final apiNotes = await _getNoteApiService.getAllNotesV2();

      // create map of local notes by backendNoteId
      final localNotesMap = {
        for (var note in localNotes) note.backendNoteId: note
      };

      // create map of api notes by backendNoteId
      final apiNotesMap = {for (var note in apiNotes) note.backendNoteId: note};

      // 1. delete notes in local but not in api
      final notesToDelete = localNotes.where(
          (localNote) => !apiNotesMap.containsKey(localNote.backendNoteId));
      if (notesToDelete.isNotEmpty) {
        await HiveService()
            .noteBox
            .deleteAll(notesToDelete.map((note) => note.id));
      }

      // 2. update and add new notes from api
      for (var apiNote in apiNotes) {
        final localNote = localNotesMap[apiNote.backendNoteId];

        // get folder name
        final folderName = HiveService()
            .folderBox
            .values
            .firstWhereOrNull((folder) => folder.backendId == apiNote.folderId)
            ?.folderName;

        if (localNote != null) {
          // update note if it is already in local
          await HiveService().noteBox.put(
                localNote.id,
                apiNote.copyWith(
                  id: localNote.id, // keep local id
                  noteStatus: NoteStatus.success,
                  folderName: folderName,
                ),
              );
        } else {
          // add new note
          await HiveService().createOrUpdateNote(
            apiNote.id,
            apiNote.copyWith(
              noteStatus: NoteStatus.success,
              folderName: folderName,
            ),
          );
        }
      }
    } catch (err) {
      // CommonDialogs.showToast(err.toString());
    }
  }

  void applyFilterAndSort() {
    var notes = HiveService().noteBox.values.toList();

    // Sync folder names for all notes
    notes = notes.map((note) {
      final folder = HiveService()
          .folderBox
          .values
          .firstWhereOrNull((folder) => folder.backendId == note.folderId);

      return note.copyWith(folderName: folder?.folderName ?? note.folderName);
    }).toList();

    switch (state.selectedFilterIndex) {
      case 0: // All
        break;
      case 1: // Record
        notes =
            notes.where((note) => note.type == FilterType.record.text).toList();
        break;
      case 2: // Upload Audio
        notes = notes
            .where((note) => note.type == FilterType.uploadAudio.text)
            .toList();
        break;
      case 3: // YouTube
        notes = notes
            .where((note) => note.type == FilterType.youtube.text)
            .toList();
        break;
      case 4: // Web Link
        notes = notes
            .where((note) => note.type == FilterType.webLink.text)
            .toList();
        break;
      case 5: // Document
        notes = notes
            .where((note) => note.type == FilterType.document.text)
            .toList();
        break;
    }

    switch (state.selectedSortIndex) {
      case 0: // Newest First
        notes.sort((a, b) => b.timeStamp.compareTo(a.timeStamp));
        break;
      case 1: // Oldest First
        notes.sort((a, b) => a.timeStamp.compareTo(b.timeStamp));
        break;
      case 2: // A to Z
        notes.sort(
            (a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));
        break;
      case 3: // Z to A
        notes.sort(
            (a, b) => b.title.toLowerCase().compareTo(a.title.toLowerCase()));
        break;
    }
    emit(state.copyWith(filteredNotes: notes));
  }

  void initTabBars(TickerProvider tickerProvider) async {
    tabBarController =
        TabController(length: 3, vsync: tickerProvider, initialIndex: 0);
    pageController = PageController();

    tabBarController.addListener(() {
      emit(
          state.copyWith(currentSelectedTabBarIndex: (tabBarController.index)));
    });

    pageController.addListener(() {
      if (!_isPageControllerReady) {
        _isPageControllerReady = true;
      }
    });
  }

  Future<void> safeNavigateToPage(int page) async {
    try {
      if (!_isPageControllerReady) {
        await Future.delayed(const Duration(milliseconds: 100));
        if (pageController.hasClients) {
          pageController.jumpToPage(page);
        }
      } else {
        pageController.jumpToPage(page);
      }
    } catch (e) {
      debugPrint('safeNavigateToPage error: $e');
    }
  }

  /// UI event handlers for the home page navigation and actions.
  /// Each method tracks analytics events for their respective actions.
  void onSettingsTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_setting,
    );
  }

  void onSearchTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_search,
    );
  }

  void onProTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_pro,
    );
  }

  void onTabBarTap(int index) {
    final eventName = switch (index) {
      0 => EventName.home_tabbar_note,
      1 => EventName.home_tabbar_folder,
      2 => EventName.home_tabbar_shared,
      _ => EventName.home_tabbar_note
    };
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: eventName,
    );
  }

  void onAllNotesTabTap() {
    final isAlreadyOnAllNotesTab = tabBarController.index == 0;
    final eventName = isAlreadyOnAllNotesTab
        ? EventName.home_filter
        : EventName.home_tabbar_note;

    AnalyticsService.logAnalyticsEventNoParam(
      eventName: eventName,
    );
  }

  Future<void> onRefreshNote() async {
    await refreshAllNotes();
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_refresh_note,
    );
  }

  void onRefreshFolder() {
    getAllFolders();
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_refresh_folder,
    );
  }

  void onNoteItemTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_note_item_tap,
    );
  }

  void onCommunityNoteItemTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_community_notes,
    );
  }

  void setShowEventOverlay(bool isShow) {
    emit(state.copyWith(showEventOverlay: isShow));
  }

  void onBottomBarNewNoteRecordTabTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_record,
    );
  }

  /// Methods for handling bottom sheet interactions and events.
  /// These methods manage filter/sort selections and track related analytics events.
  void onFilterAndSortBottomSheetCloseTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_tabbar_note_close_filter,
    );
  }

  void initListnerOnMultiSelectNote() {
    _multiSelectController.addListener(() {
      if (_multiSelectController.isMultiSelectMode) {
        _bottomNavKey.currentState?.hide();
      } else {
        _bottomNavKey.currentState?.show();
      }
    });
  }

  void onFilterSelectionUpdated(int index) {
    if (index != state.selectedFilterIndex) {
      emit(state.copyWith(selectedFilterIndex: index));
      applyFilterAndSort();
    }
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_tabbar_note_filter_updated,
    );
  }

  void onSortSelectionUpdated(int index) {
    if (index != state.selectedSortIndex) {
      emit(state.copyWith(selectedSortIndex: index));
      applyFilterAndSort();
    }
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_tabbar_note_sort_updated,
    );
  }

  String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return S.current.good_morning;
    } else if (hour < 17) {
      return S.current.good_afternoon;
    } else {
      return S.current.good_evening;
    }
  }

  List<String> getContentGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return [
        S.current.morning_content,
        S.current.morning_content_2,
        S.current.morning_content_3,
        S.current.morning_content_4,
        S.current.morning_content_5,
        S.current.morning_content_6,
      ];
    } else if (hour < 17) {
      return [
        S.current.afternoon_content_1,
        S.current.afternoon_content,
        S.current.afternoon_content_3,
        S.current.afternoon_content_4,
        S.current.afternoon_content_5,
        S.current.afternoon_content_6,
      ];
    } else {
      return [
        S.current.evening_content,
        S.current.evening_content_2,
        S.current.evening_content_3,
        S.current.evening_content_4,
        S.current.evening_content_5,
        S.current.evening_content_6,
      ];
    }
  }
}
