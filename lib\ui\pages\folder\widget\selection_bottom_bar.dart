import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class SelectionBottomBar extends StatelessWidget {
  final VoidCallback? onMovePressed;
  final VoidCallback? onDeletePressed;
  final bool isTablet;

  const SelectionBottomBar({
    Key? key,
    required this.isTablet,
    this.onMovePressed,
    this.onDeletePressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 16 : 16.w,
        vertical: isTablet ? 34 : 34.h,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildActionButton(
            context: context,
            icon: Assets.icons.icMoveFolderDetail,
            label: S.current.move,
            color: context.colorScheme.mainPrimary,
            onTap: onMovePressed,
          ),
          _buildActionButton(
            context: context,
            icon: Assets.icons.icDelete,
            label: S.current.delete,
            color: context.colorScheme.mainRed,
            iconColor: Colors.red,
            onTap: onDeletePressed,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required String icon,
    required String label,
    required Color color,
    Color? iconColor,
    required VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? 12 : 12.w,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              icon,
              width: 24.w,
              height: 24.w,
              colorFilter: iconColor != null
                  ? ColorFilter.mode(iconColor, BlendMode.srcIn)
                  : null,
            ),
            SizedBox(height: 4.h),
            CommonText(
              label,
              style: TextStyle(
                color: color,
                fontSize: isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
