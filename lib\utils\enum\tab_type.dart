import 'package:note_x/lib.dart';

enum TabType {
  summary,
  transcript,
  slideShow,
  mindMap,
  shorts,
  documentTab,
  flashcard,
  quizzes,
  podcast;

  String get displayName {
    switch (this) {
      case TabType.summary:
        return S.current.summary;
      case TabType.transcript:
        return S.current.transcript;
      case TabType.slideShow:
        return S.current.slide_show;
      case TabType.mindMap:
        return S.current.mind_map;
      case TabType.shorts:
        return S.current.shorts;
      case TabType.documentTab:
        return S.current.document_tab;
      case TabType.flashcard:
        return S.current.flashcard;
      case TabType.quizzes:
        return S.current.quizzes;
      case TabType.podcast:
        return S.current.podcast;
    }
  }

  String get iconPath {
    switch (this) {
      case TabType.summary:
        return Assets.icons.icHomeDoc;
      case TabType.transcript:
        return Assets.icons.icObTrans;
      case TabType.slideShow:
        return Assets.icons.icSlide01;
      case TabType.mindMap:
        return Assets.icons.icMindmapLayoutB;
      case TabType.shorts:
        return Assets.icons.icCustomShort;
      case TabType.documentTab:
        return Assets.icons.icCustomDoc;
      case TabType.flashcard:
        return Assets.icons.icCustomFlashcard;
      case TabType.quizzes:
        return Assets.icons.icCustomQuiz;
      case TabType.podcast:
        return Assets.icons.icCustomPodcast;
    }
  }

  static TabType fromString(String value) {
    return TabType.values.firstWhere(
          (type) => type.displayName == value,
      orElse: () => TabType.summary,
    );
  }

  static List<TabType> fromStringList(List<String> values) {
    return values.map((value) => fromString(value)).toList();
  }

  static List<String> toStringList(List<TabType> types) {
    return types.map((type) => type.displayName).toList();
  }
}