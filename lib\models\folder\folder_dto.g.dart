// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'folder_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FolderDtoImpl _$$FolderDtoImplFromJson(Map<String, dynamic> json) =>
    _$FolderDtoImpl(
      id: json['id'] as String? ?? '',
      folderName: json['folder_name'] as String? ?? '',
      parentFolderId: json['parent_folder_id'] as String?,
      path: json['path'] as String? ?? '',
      level: (json['level'] as num?)?.toInt() ?? 0,
      noteCount: (json['note_count'] as num?)?.toInt() ?? 0,
      subfolders: (json['subfolders'] as List<dynamic>?)
              ?.map((e) => FolderDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <FolderDto>[],
      notes: (json['notes'] as List<dynamic>?)
              ?.map((e) => NoteDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <NoteDto>[],
    );

Map<String, dynamic> _$$FolderDtoImplToJson(_$FolderDtoImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'folder_name': instance.folderName,
      'parent_folder_id': instance.parentFolderId,
      'path': instance.path,
      'level': instance.level,
      'note_count': instance.noteCount,
      'subfolders': instance.subfolders,
      'notes': instance.notes,
    };
