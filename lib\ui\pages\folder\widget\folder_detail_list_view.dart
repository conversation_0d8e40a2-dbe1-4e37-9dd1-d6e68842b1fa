import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/folder/widget/common_folder_item_widget.dart';
import 'package:note_x/ui/pages/folder/widget/folder_detail_list_view_data.dart';
import 'package:note_x/ui/pages/folder/widget/folder_view_data.dart';
import 'package:note_x/ui/pages/folder/widget/selection_bottom_bar.dart';

class FolderDetailListView extends StatefulWidget {
  final FolderDetailListViewData viewData;
  final Function(String)? onDeleteNote;
  final Function(String)? onDeleteFolder;
  final Function()? onNoteItemTap;
  final Function()? onFolderItemTap;
  final Function(List<String>)? onSelectedItemsChanged;
  final MultiSelectController? controller;

  const FolderDetailListView({
    super.key,
    required this.viewData,
    this.onDeleteNote,
    this.onDeleteFolder,
    this.onNoteItemTap,
    this.onFolderItemTap,
    this.onSelectedItemsChanged,
    this.controller,
  });

  @override
  State<FolderDetailListView> createState() => _FolderDetailListViewState();
}

class _FolderDetailListViewState extends State<FolderDetailListView>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final _localService = GetIt.instance.get<LocalService>();

  // Multi-select controller
  late MultiSelectController _multiSelectController;

  // Animation controller for smooth transitions
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Initialize multi-select controller
    _multiSelectController = widget.controller ?? MultiSelectController();

    // Set initial animation state
    _animationController.value = 0.0;

    // Listen to multi-select changes for animation
    _multiSelectController.addListener(_onMultiSelectChanged);

    // Add scroll listener
    _scrollController.addListener(() {
      if (HomeOverlayManager().hasActiveOverlays &&
          _scrollController.position.userScrollDirection !=
              ScrollDirection.idle) {
        HomeOverlayManager().closeAllOverlays();
      }
    });
  }

  void _onMultiSelectChanged() {
    if (_multiSelectController.isMultiSelectMode) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    // Notify parent of selection changes
    widget.onSelectedItemsChanged
        ?.call(_multiSelectController.selectedNoteIds.toList());
  }

  @override
  void dispose() {
    _multiSelectController.removeListener(_onMultiSelectChanged);
    if (widget.controller == null) {
      _multiSelectController.dispose();
    }
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  // Multi-select functions
  void selectAll() {
    _multiSelectController.selectAll(widget.viewData.allItemIds);
  }

  void deselectAll() {
    _multiSelectController.deselectAll();
  }

  void _toggleItemSelection(String itemId) {
    _multiSelectController.toggleNoteSelection(itemId);
  }

  void _enterMultiSelectMode(String itemId) {
    _multiSelectController.enterMultiSelectMode(itemId);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.viewData.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListenableBuilder(
      listenable: _multiSelectController,
      builder: (context, _) {
        return Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: _multiSelectController.isMultiSelectMode ? 48 : 0,
              child: _multiSelectController.isMultiSelectMode
                  ? _buildSelectDeselectWidget(context)
                  : const SizedBox.shrink(),
            ),
            // Item list
            Expanded(child: _buildItemListView(context)),
            // Selection bottom bar (only visible in multi-select mode)
            if (_multiSelectController.isMultiSelectMode)
              SelectionBottomBar(
                isTablet: context.isTablet,
                onDeletePressed: () {
                  final selectedIds = _multiSelectController.selectedNoteIds;
                  _handleDeleteSelectedItems(selectedIds);
                },
              ),
          ],
        );
      },
    );
  }

  void _handleDeleteSelectedItems(Set<String> selectedIds) {
    for (final id in selectedIds) {
      final item = widget.viewData.getItemById(id);
      if (item != null) {
        if (item.isNote) {
          widget.onDeleteNote?.call(id);
        } else if (item.isFolder) {
          widget.onDeleteFolder?.call(id);
        }
      }
    }
  }

  Widget _buildSelectDeselectWidget(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Select All / Deselect All button (far left)
        TextButton(
          onPressed: () {
            if (_multiSelectController.selectedCount ==
                widget.viewData.totalCount) {
              // If all items are selected, deselect all
              deselectAll();
            } else {
              // If not all items are selected, select all
              selectAll();
            }
          },
          child: Text(
            _multiSelectController.selectedCount == widget.viewData.totalCount
                ? 'Deselect All'
                : 'Select All',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: context.colorScheme.mainBlue,
            ),
          ),
        ),

        // Selected count indicator (center)
        Text(
          '${_multiSelectController.selectedCount} selected',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray,
          ),
        ),

        // Cancel button (far right)
        TextButton(
          onPressed: () {
            _multiSelectController.exitMultiSelectMode();
          },
          child: Text(
            'Cancel',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: context.colorScheme.mainGray,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                Assets.icons.icEmptyNoteNotexEmpty,
                width: 140,
                height: 140,
              ),
              AppConstants.kSpacingItem12,
              CommonText(
                'No items found',
                style: TextStyle(
                  fontSize: context.isTablet ? 18 : 16.sp,
                  fontWeight: FontWeight.w400,
                  color: context.colorScheme.mainGray,
                  fontFamily: AppConstants.fontSFPro,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemListView(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).padding.bottom,
        left: context.isTablet ? 24 : 0,
        right: context.isTablet ? 24 : 0,
      ),
      itemCount: widget.viewData.totalCount,
      itemBuilder: (context, index) {
        final item = widget.viewData.getItemAt(index);
        return _buildItem(context, item, index);
      },
    );
  }

  Widget _buildItem(
      BuildContext context, FolderDetailItemData item, int index) {
    // Get the item's position in the list view
    final listViewOffset = _scrollController.offset;

    // Calculate item height and position
    final double itemHeight = context.isTablet ? 80.0 : 72.0;
    final double itemPadding = context.isTablet ? 16.0 : 12.0;
    final double totalItemHeight = itemHeight + itemPadding;

    // Calculate the position of this item in the viewport
    final double itemPosition = index * totalItemHeight;
    final double itemPositionInViewport = itemPosition - listViewOffset;

    // Calculate the visible viewport height
    final double viewportHeight = MediaQuery.of(context).size.height -
        kToolbarHeight -
        MediaQuery.of(context).padding.top -
        (context.isTablet ? 80.0 : 60.0) -
        (context.isTablet ? 75.0 : 72.0) -
        MediaQuery.of(context).padding.bottom;

    // Determine if the item is in the bottom half of the visible area
    final bool isInBottomHalf = itemPositionInViewport > (viewportHeight / 2);

    return FadeAnimation(
      (1.0 + index) / 4,
      Padding(
        padding: context.isTablet
            ? const EdgeInsets.symmetric(horizontal: 10, vertical: 8)
            : EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
        child: ListenableBuilder(
          listenable: _multiSelectController,
          builder: (context, child) {
            final bool isSelected = _multiSelectController.isSelected(item.id);
            final bool isMultiSelectMode =
                _multiSelectController.isMultiSelectMode;

            if (item.isFolder) {
              return _buildFolderItem(
                context,
                item.folderData,
                isMultiSelectMode,
                isSelected,
                item.id,
              );
            } else {
              return _buildNoteItem(
                context,
                item.noteData,
                index,
                isMultiSelectMode,
                isSelected,
                isInBottomHalf,
                totalItemHeight,
                viewportHeight,
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildFolderItem(
    BuildContext context,
    FolderModel folder,
    bool isMultiSelectMode,
    bool isSelected,
    String itemId,
  ) {
    // You'll need to create a FolderViewData from your FolderModel
    // This is a simplified example - adjust according to your actual FolderViewData structure
    final viewData = FolderViewData(
      id: folder.id,
      folderName: folder.folderName,
      numberOfNotes: 0, // You might need to calculate this
      subfoldersCount: 0, // You might need to calculate this
      icon: Assets.icons.icFolder, // Use appropriate icon
      showOptions: !isMultiSelectMode,
      onTap: () {
        widget.onFolderItemTap?.call();
        // Navigate to folder detail or handle folder tap
      },
      // Add other required parameters
    );

    return CommonFolderItemWidget(
      viewData: viewData,
      isTablet: context.isTablet,
      isMultiSelectMode: isMultiSelectMode,
      isSelected: isSelected,
      onSelectionTap: () => _toggleItemSelection(itemId),
      onLongPress: () {
        if (!isMultiSelectMode) {
          _enterMultiSelectMode(itemId);
        }
      },
    );
  }

  Widget _buildNoteItem(
    BuildContext context,
    NoteModel note,
    int index,
    bool isMultiSelectMode,
    bool isSelected,
    bool isInBottomHalf,
    double totalItemHeight,
    double viewportHeight,
  ) {
    return HomeItemNoteWidget(
      noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
      isMultiSelectMode: isMultiSelectMode,
      isSelected: isSelected,
      onTap: () {
        widget.onNoteItemTap?.call();
        _navigateToNoteDetail(context, note);
      },
      onLongPress: () {
        if (!isMultiSelectMode) {
          _enterMultiSelectMode(note.id);
        }
      },
      onSelectionTap: () => _toggleItemSelection(note.id),
      overflowMenu: HomeOverFlowMenu(
        note: note,
        isCommunityNote: false,
        onShowFolder: (context) {
          MoveToFolderHelper.showBottomSheet(
            context,
            notesToBeMoved: [note],
            foldersToBeMovedIds: [note.folderId],
          );
        },
        onShowBottomSheet: (exportType) {
          ExportNoteHelper.showBottomSheet(context, exportType, note);
        },
        focusNode: FocusNode(),
        onShowSharingView: Func0(() {
          NoteSharingHelper.showBottomSheet(context, note);
        }),
        onDeleteNote: () {
          widget.onDeleteNote?.call(note.id);
        },
        isInBottomHalf: isInBottomHalf,
        calculateIsInBottomHalf: () {
          final currentListViewOffset = _scrollController.offset;
          final currentItemPosition = index * totalItemHeight;
          final currentPositionInViewport =
              currentItemPosition - currentListViewOffset;
          final itemBottomPosition =
              currentPositionInViewport + totalItemHeight;
          return itemBottomPosition > (viewportHeight * 0.7);
        },
      ),
    );
  }

  void _navigateToNoteDetail(BuildContext context, NoteModel note) async {
    // Get saved tabs before navigation
    final savedTabs = await _initTabBar();

    if (!mounted) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MyNoteDetailPage(
          isCommunityNote: false,
          noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
          isTablet: context.isTablet,
          from: NoteDetailPageFrom.homeScreen,
          savedTabs: savedTabs,
        ),
      ),
    );
  }

  Future<List<TabType>> _initTabBar() async {
    return await _localService.loadSelectedItems();
  }
}
