import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/shorts_detail/short_details_state.dart';

/// Common widgets shared between ShortsDetailPage and PodcastDetailPage
class ShortsDetailCommonWidgets {
  /// Builds the top app bar with title and close button
  static Widget buildTopBar({
    required BuildContext context,
    required ValueNotifier<String> titleNotifier,
    required VoidCallback onCancelTap,
    String? overrideTitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Close button
          GestureDetector(
            onTap: onCancelTap,
            child: SvgPicture.asset(
              Assets.icons.icCloseWhite,
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
          // Title (centered)
          Expanded(
            child: Center(
              child: ValueListenableBuilder<String>(
                valueListenable: titleNotifier,
                builder: (context, title, child) {
                  return CommonText(
                    overrideTitle ?? title,
                    style: TextStyle(
                      fontSize: context.isTablet ? 18 : 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 24), // Balance for close button
        ],
      ),
    );
  }

  /// Shows a confirmation dialog when user attempts to cancel/close
  static void showCancelConfirmationDialog(BuildContext context) {
    showNewCupertinoDialog(
      context: context,
      title: S.current.discard_changes,
      message: S.current.audio_video_not_save,
      image: Assets.icons.icDiscardChanges,
      cancelButton: S.current.cancel,
      confirmButton: S.current.discard,
      onCancel: () {},
      onConfirm: () => Navigator.of(context).pop(),
    );
  }

  /// Builds the bottom controls for sharing and exporting content
  static Widget buildBottomControls({
    required BuildContext context,
    required GlobalKey shareButtonKey,
    required GlobalKey downloadButtonKey,
    required bool isAudio,
    required bool isTablet,
    required Function(BuildContext,
            {required GlobalKey buttonKey, required bool isDownload})
        onDownloadOrShare,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.isTablet ? 100 : 16.w,
        vertical: context.isTablet ? 8 : 8.0.h,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Download button
          buildShareButton(
            context: context,
            buttonKey: downloadButtonKey,
            onDownload: () => onDownloadOrShare(context,
                buttonKey: downloadButtonKey, isDownload: false),
          ),
          SizedBox(width: 8.w),
          // Share/export button
          Flexible(
            child: buildDownloadButton(
              context: context,
              buttonKey: shareButtonKey,
              isAudio: isAudio,
              isTablet: isTablet,
              onShare: () => onDownloadOrShare(context,
                  buttonKey: shareButtonKey, isDownload: true),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the download button
  static Widget buildShareButton({
    required BuildContext context,
    required GlobalKey buttonKey,
    required VoidCallback onDownload,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(24.r),
      ),
      width: context.isTablet ? 48 : 48.w,
      height: context.isTablet ? 48 : 48.w,
      alignment: Alignment.center,
      child: GestureDetector(
        onTap: onDownload,
        child: SvgPicture.asset(
          Assets.icons.icShare3,
          width: context.isTablet ? 24 : 24.w,
          height: context.isTablet ? 24 : 24.h,
          fit: BoxFit.contain,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainPrimary,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }

  /// Builds the share/export button
  static Widget buildDownloadButton({
    required BuildContext context,
    required GlobalKey buttonKey,
    required bool isAudio,
    required bool isTablet,
    required VoidCallback onShare,
  }) {
    return AppCommonButton(
      key: buttonKey,
      width: AppConstants.appWidth,
      height: context.isTablet ? 48 : 48.h,
      borderRadius: BorderRadius.circular(24.r),
      textWidget: Text(
        isAudio ? S.current.export_audio : S.current.export_video,
        style: TextStyle(
          fontSize: isTablet ? 18 : 16.sp,
          fontWeight: FontWeight.w500,
          color: context.colorScheme.themeWhite,
        ),
      ),
      leftIcon: SvgPicture.asset(
        Assets.icons.icShareShort,
        colorFilter: const ColorFilter.mode(
          Colors.white,
          BlendMode.srcIn,
        ),
      ),
      onPressed: onShare,
      backgroundColor: context.colorScheme.mainBlue,
    );
  }

  /// Builds the temporary content message
  static Widget buildTemporaryContentMessage({
    required BuildContext context,
    required bool isAudio,
    required bool isTablet,
  }) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: CommonText(
        isAudio ? S.current.audio_is_temporary : S.current.video_is_temporary,
        style: TextStyle(
          color: context.colorScheme.mainGray,
          fontSize: isTablet ? 12 : 10.sp,
        ),
      ),
    );
  }

  /// Builds the processing overlay with status message
  static Widget buildProcessingOverlay({
    required BuildContext context,
    required ProcessingStatus status,
  }) {
    // Determine message based on processing status
    String message;
    switch (status) {
      case ProcessingStatus.downloading:
        message = S.current.generate_shorts_step_2;
        break;
      case ProcessingStatus.processing:
        message = S.current.generate_shorts_step_3;
        break;
      case ProcessingStatus.apiGenerating:
        message = S.current.generate_shorts_step_1;
        break;
      default:
        return const SizedBox.shrink();
    }

    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: context.colorScheme.mainNeutral.withOpacity(0.8),
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Loading animation
            Lottie.asset(
              Assets.videos.shortsCreating,
              width: context.isTablet ? 36 : 40.w,
              height: context.isTablet ? 36 : 40.w,
            ),
            SizedBox(width: context.isTablet ? 8 : 16.w),
            // Status message
            Flexible(
              child: CommonText(
                message,
                style: TextStyle(
                  color: context.colorScheme.mainPrimary,
                  fontSize: context.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w400,
                ),
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget buildProcessingOverlayShortQuiz({
    required BuildContext context,
    required ShortQuizOneShotEvent status,
    String? progressMessage,
  }) {
    String message = progressMessage ?? S.current.generate_shorts_step_1;

    if (status != ShortQuizOneShotEvent.loading) {
      return const SizedBox.shrink();
    }

    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: context.colorScheme.mainSecondary.withOpacity(0.8),
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Loading animation
            Lottie.asset(
              Assets.videos.shortsCreating,
              width: context.isTablet ? 36 : 40.w,
              height: context.isTablet ? 36 : 40.w,
            ),
            SizedBox(width: context.isTablet ? 8 : 16.w),
            // Status message
            Flexible(
              child: CommonText(
                message,
                style: TextStyle(
                  color: context.colorScheme.mainPrimary,
                  fontSize: context.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w400,
                ),
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
