/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/ic_5_start.svg
  String get ic5Start => 'assets/icons/ic_5_start.svg';

  /// File path: assets/icons/ic_account_check_blue.svg
  String get icAccountCheckBlue => 'assets/icons/ic_account_check_blue.svg';

  /// File path: assets/icons/ic_account_check_gray.svg
  String get icAccountCheckGray => 'assets/icons/ic_account_check_gray.svg';

  /// File path: assets/icons/ic_account_check_violet.svg
  String get icAccountCheckViolet => 'assets/icons/ic_account_check_violet.svg';

  /// File path: assets/icons/ic_account_check_yellow.svg
  String get icAccountCheckYellow => 'assets/icons/ic_account_check_yellow.svg';

  /// File path: assets/icons/ic_action_items.svg
  String get icActionItems => 'assets/icons/ic_action_items.svg';

  /// File path: assets/icons/ic_add.svg
  String get icAdd => 'assets/icons/ic_add.svg';

  /// File path: assets/icons/ic_add_folder.svg
  String get icAddFolder => 'assets/icons/ic_add_folder.svg';

  /// File path: assets/icons/ic_add_image.svg
  String get icAddImage => 'assets/icons/ic_add_image.svg';

  /// File path: assets/icons/ic_add_image_light_mode.svg
  String get icAddImageLightMode => 'assets/icons/ic_add_image_light_mode.svg';

  /// File path: assets/icons/ic_add_specific.svg
  String get icAddSpecific => 'assets/icons/ic_add_specific.svg';

  /// File path: assets/icons/ic_additional.svg
  String get icAdditional => 'assets/icons/ic_additional.svg';

  /// File path: assets/icons/ic_ai_chat.svg
  String get icAiChat => 'assets/icons/ic_ai_chat.svg';

  /// File path: assets/icons/ic_ai_gen.svg
  String get icAiGen => 'assets/icons/ic_ai_gen.svg';

  /// File path: assets/icons/ic_ai_star.svg
  String get icAiStar => 'assets/icons/ic_ai_star.svg';

  /// File path: assets/icons/ic_anonymous_eye.svg
  String get icAnonymousEye => 'assets/icons/ic_anonymous_eye.svg';

  /// File path: assets/icons/ic_appbar_arrow_right.svg
  String get icAppbarArrowRight => 'assets/icons/ic_appbar_arrow_right.svg';

  /// File path: assets/icons/ic_apple.svg
  String get icApple => 'assets/icons/ic_apple.svg';

  /// File path: assets/icons/ic_apple_watch.svg
  String get icAppleWatch => 'assets/icons/ic_apple_watch.svg';

  /// File path: assets/icons/ic_arrow_left.svg
  String get icArrowLeft => 'assets/icons/ic_arrow_left.svg';

  /// File path: assets/icons/ic_arrow_right.svg
  String get icArrowRight => 'assets/icons/ic_arrow_right.svg';

  /// File path: assets/icons/ic_arrow_setting.svg
  String get icArrowSetting => 'assets/icons/ic_arrow_setting.svg';

  /// File path: assets/icons/ic_arrow_up.svg
  String get icArrowUp => 'assets/icons/ic_arrow_up.svg';

  /// File path: assets/icons/ic_arrow_up2.svg
  String get icArrowUp2 => 'assets/icons/ic_arrow_up2.svg';

  /// File path: assets/icons/ic_auto_count.svg
  String get icAutoCount => 'assets/icons/ic_auto_count.svg';

  /// File path: assets/icons/ic_average_rating.svg
  String get icAverageRating => 'assets/icons/ic_average_rating.svg';

  /// File path: assets/icons/ic_balanced.svg
  String get icBalanced => 'assets/icons/ic_balanced.svg';

  /// File path: assets/icons/ic_bell.svg
  String get icBell => 'assets/icons/ic_bell.svg';

  /// File path: assets/icons/ic_blue_eye.svg
  String get icBlueEye => 'assets/icons/ic_blue_eye.svg';

  /// File path: assets/icons/ic_blue_target.svg
  String get icBlueTarget => 'assets/icons/ic_blue_target.svg';

  /// File path: assets/icons/ic_business.svg
  String get icBusiness => 'assets/icons/ic_business.svg';

  /// File path: assets/icons/ic_calendar.svg
  String get icCalendar => 'assets/icons/ic_calendar.svg';

  /// File path: assets/icons/ic_calendar_edit_note.svg
  String get icCalendarEditNote => 'assets/icons/ic_calendar_edit_note.svg';

  /// File path: assets/icons/ic_camera.svg
  String get icCamera => 'assets/icons/ic_camera.svg';

  /// File path: assets/icons/ic_camera_mascot.svg
  String get icCameraMascot => 'assets/icons/ic_camera_mascot.svg';

  /// File path: assets/icons/ic_camera_scan.svg
  String get icCameraScan => 'assets/icons/ic_camera_scan.svg';

  /// File path: assets/icons/ic_camera_star.svg
  String get icCameraStar => 'assets/icons/ic_camera_star.svg';

  /// File path: assets/icons/ic_card_count.svg
  String get icCardCount => 'assets/icons/ic_card_count.svg';

  /// File path: assets/icons/ic_chat.svg
  String get icChat => 'assets/icons/ic_chat.svg';

  /// File path: assets/icons/ic_chat_app_bar.svg
  String get icChatAppBar => 'assets/icons/ic_chat_app_bar.svg';

  /// File path: assets/icons/ic_check_black.svg
  String get icCheckBlack => 'assets/icons/ic_check_black.svg';

  /// File path: assets/icons/ic_check_blue.svg
  String get icCheckBlue => 'assets/icons/ic_check_blue.svg';

  /// File path: assets/icons/ic_check_circle.svg
  String get icCheckCircle => 'assets/icons/ic_check_circle.svg';

  /// File path: assets/icons/ic_check_circle_1.svg
  String get icCheckCircle1 => 'assets/icons/ic_check_circle_1.svg';

  /// File path: assets/icons/ic_check_create_note.svg
  String get icCheckCreateNote => 'assets/icons/ic_check_create_note.svg';

  /// File path: assets/icons/ic_check_green.svg
  String get icCheckGreen => 'assets/icons/ic_check_green.svg';

  /// File path: assets/icons/ic_check_ob.svg
  String get icCheckOb => 'assets/icons/ic_check_ob.svg';

  /// File path: assets/icons/ic_check_promotion.svg
  String get icCheckPromotion => 'assets/icons/ic_check_promotion.svg';

  /// File path: assets/icons/ic_check_radian.svg
  String get icCheckRadian => 'assets/icons/ic_check_radian.svg';

  /// File path: assets/icons/ic_check_radian_green.svg
  String get icCheckRadianGreen => 'assets/icons/ic_check_radian_green.svg';

  /// File path: assets/icons/ic_choose_type.svg
  String get icChooseType => 'assets/icons/ic_choose_type.svg';

  /// File path: assets/icons/ic_circle_add.svg
  String get icCircleAdd => 'assets/icons/ic_circle_add.svg';

  /// File path: assets/icons/ic_close.svg
  String get icClose => 'assets/icons/ic_close.svg';

  /// File path: assets/icons/ic_close_black.svg
  String get icCloseBlack => 'assets/icons/ic_close_black.svg';

  /// File path: assets/icons/ic_close_circle.svg
  String get icCloseCircle => 'assets/icons/ic_close_circle.svg';

  /// File path: assets/icons/ic_close_iap.svg
  String get icCloseIap => 'assets/icons/ic_close_iap.svg';

  /// File path: assets/icons/ic_close_search.svg
  String get icCloseSearch => 'assets/icons/ic_close_search.svg';

  /// File path: assets/icons/ic_close_white.svg
  String get icCloseWhite => 'assets/icons/ic_close_white.svg';

  /// File path: assets/icons/ic_coin_credits.svg
  String get icCoinCredits => 'assets/icons/ic_coin_credits.svg';

  /// File path: assets/icons/ic_coin_earned.svg
  String get icCoinEarned => 'assets/icons/ic_coin_earned.svg';

  /// File path: assets/icons/ic_coin_used.svg
  String get icCoinUsed => 'assets/icons/ic_coin_used.svg';

  /// File path: assets/icons/ic_common_dialog_error.svg
  String get icCommonDialogError => 'assets/icons/ic_common_dialog_error.svg';

  /// File path: assets/icons/ic_common_dialog_error_header_image.svg
  String get icCommonDialogErrorHeaderImage =>
      'assets/icons/ic_common_dialog_error_header_image.svg';

  /// File path: assets/icons/ic_common_info_tooltip.svg
  String get icCommonInfoTooltip => 'assets/icons/ic_common_info_tooltip.svg';

  /// File path: assets/icons/ic_component.svg
  String get icComponent => 'assets/icons/ic_component.svg';

  /// File path: assets/icons/ic_computer.svg
  String get icComputer => 'assets/icons/ic_computer.svg';

  /// File path: assets/icons/ic_congratulations.svg
  String get icCongratulations => 'assets/icons/ic_congratulations.svg';

  /// File path: assets/icons/ic_connect_internet.svg
  String get icConnectInternet => 'assets/icons/ic_connect_internet.svg';

  /// File path: assets/icons/ic_copy.svg
  String get icCopy => 'assets/icons/ic_copy.svg';

  /// File path: assets/icons/ic_copy_chat.svg
  String get icCopyChat => 'assets/icons/ic_copy_chat.svg';

  /// File path: assets/icons/ic_copy_referral.svg
  String get icCopyReferral => 'assets/icons/ic_copy_referral.svg';

  /// File path: assets/icons/ic_correct.svg
  String get icCorrect => 'assets/icons/ic_correct.svg';

  /// File path: assets/icons/ic_create_folder.svg
  String get icCreateFolder => 'assets/icons/ic_create_folder.svg';

  /// File path: assets/icons/ic_create_new_folder.svg
  String get icCreateNewFolder => 'assets/icons/ic_create_new_folder.svg';

  /// File path: assets/icons/ic_create_note_done.svg
  String get icCreateNoteDone => 'assets/icons/ic_create_note_done.svg';

  /// File path: assets/icons/ic_create_note_drop_down.svg
  String get icCreateNoteDropDown =>
      'assets/icons/ic_create_note_drop_down.svg';

  /// File path: assets/icons/ic_create_note_pause.svg
  String get icCreateNotePause => 'assets/icons/ic_create_note_pause.svg';

  /// File path: assets/icons/ic_create_note_record.svg
  String get icCreateNoteRecord => 'assets/icons/ic_create_note_record.svg';

  /// File path: assets/icons/ic_create_sub_folder.svg
  String get icCreateSubFolder => 'assets/icons/ic_create_sub_folder.svg';

  /// File path: assets/icons/ic_crown.svg
  String get icCrown => 'assets/icons/ic_crown.svg';

  /// File path: assets/icons/ic_crown_setting.svg
  String get icCrownSetting => 'assets/icons/ic_crown_setting.svg';

  /// File path: assets/icons/ic_cup.svg
  String get icCup => 'assets/icons/ic_cup.svg';

  /// File path: assets/icons/ic_custom_doc.svg
  String get icCustomDoc => 'assets/icons/ic_custom_doc.svg';

  /// File path: assets/icons/ic_custom_flashcard.svg
  String get icCustomFlashcard => 'assets/icons/ic_custom_flashcard.svg';

  /// File path: assets/icons/ic_custom_podcast.svg
  String get icCustomPodcast => 'assets/icons/ic_custom_podcast.svg';

  /// File path: assets/icons/ic_custom_quiz.svg
  String get icCustomQuiz => 'assets/icons/ic_custom_quiz.svg';

  /// File path: assets/icons/ic_custom_short.svg
  String get icCustomShort => 'assets/icons/ic_custom_short.svg';

  /// File path: assets/icons/ic_dark_setting.svg
  String get icDarkSetting => 'assets/icons/ic_dark_setting.svg';

  /// File path: assets/icons/ic_dating_profile.svg
  String get icDatingProfile => 'assets/icons/ic_dating_profile.svg';

  /// File path: assets/icons/ic_default_avatar.svg
  String get icDefaultAvatar => 'assets/icons/ic_default_avatar.svg';

  /// File path: assets/icons/ic_delete.svg
  String get icDelete => 'assets/icons/ic_delete.svg';

  /// File path: assets/icons/ic_delete_account.svg
  String get icDeleteAccount => 'assets/icons/ic_delete_account.svg';

  /// File path: assets/icons/ic_delete_accout_dialog.svg
  String get icDeleteAccoutDialog => 'assets/icons/ic_delete_accout_dialog.svg';

  /// File path: assets/icons/ic_delete_dialog.svg
  String get icDeleteDialog => 'assets/icons/ic_delete_dialog.svg';

  /// File path: assets/icons/ic_diamond_account.svg
  String get icDiamondAccount => 'assets/icons/ic_diamond_account.svg';

  /// File path: assets/icons/ic_difficulty.svg
  String get icDifficulty => 'assets/icons/ic_difficulty.svg';

  /// File path: assets/icons/ic_discard_changes.svg
  String get icDiscardChanges => 'assets/icons/ic_discard_changes.svg';

  /// File path: assets/icons/ic_discord.svg
  String get icDiscord => 'assets/icons/ic_discord.svg';

  /// File path: assets/icons/ic_document_text.svg
  String get icDocumentText => 'assets/icons/ic_document_text.svg';

  /// File path: assets/icons/ic_dot_dark.svg
  String get icDotDark => 'assets/icons/ic_dot_dark.svg';

  /// File path: assets/icons/ic_dot_light.svg
  String get icDotLight => 'assets/icons/ic_dot_light.svg';

  /// File path: assets/icons/ic_dot_small.svg
  String get icDotSmall => 'assets/icons/ic_dot_small.svg';

  /// File path: assets/icons/ic_dowload_slide.svg
  String get icDowloadSlide => 'assets/icons/ic_dowload_slide.svg';

  /// File path: assets/icons/ic_download.svg
  String get icDownload => 'assets/icons/ic_download.svg';

  /// File path: assets/icons/ic_dropdown.svg
  String get icDropdown => 'assets/icons/ic_dropdown.svg';

  /// File path: assets/icons/ic_edit_note.svg
  String get icEditNote => 'assets/icons/ic_edit_note.svg';

  /// File path: assets/icons/ic_edit_question.svg
  String get icEditQuestion => 'assets/icons/ic_edit_question.svg';

  /// File path: assets/icons/ic_edit_reminder_time.svg
  String get icEditReminderTime => 'assets/icons/ic_edit_reminder_time.svg';

  /// File path: assets/icons/ic_edit_reminder_title.svg
  String get icEditReminderTitle => 'assets/icons/ic_edit_reminder_title.svg';

  /// File path: assets/icons/ic_edit_transcript.svg
  String get icEditTranscript => 'assets/icons/ic_edit_transcript.svg';

  /// File path: assets/icons/ic_email.svg
  String get icEmail => 'assets/icons/ic_email.svg';

  /// File path: assets/icons/ic_email2.svg
  String get icEmail2 => 'assets/icons/ic_email2.svg';

  /// File path: assets/icons/ic_empty_folder.svg
  String get icEmptyFolder => 'assets/icons/ic_empty_folder.svg';

  /// File path: assets/icons/ic_empty_note_new_user.svg
  String get icEmptyNoteNewUser => 'assets/icons/ic_empty_note_new_user.svg';

  /// File path: assets/icons/ic_empty_note_notex_empty.svg
  String get icEmptyNoteNotexEmpty =>
      'assets/icons/ic_empty_note_notex_empty.svg';

  /// File path: assets/icons/ic_empty_summary.svg
  String get icEmptySummary => 'assets/icons/ic_empty_summary.svg';

  /// File path: assets/icons/ic_error.svg
  String get icError => 'assets/icons/ic_error.svg';

  /// File path: assets/icons/ic_esential_iap.svg
  String get icEsentialIap => 'assets/icons/ic_esential_iap.svg';

  /// File path: assets/icons/ic_expand_less.svg
  String get icExpandLess => 'assets/icons/ic_expand_less.svg';

  /// File path: assets/icons/ic_expand_more.svg
  String get icExpandMore => 'assets/icons/ic_expand_more.svg';

  /// File path: assets/icons/ic_export_01.svg
  String get icExport01 => 'assets/icons/ic_export_01.svg';

  /// File path: assets/icons/ic_flash_card.svg
  String get icFlashCard => 'assets/icons/ic_flash_card.svg';

  /// File path: assets/icons/ic_flashcard_context.svg
  String get icFlashcardContext => 'assets/icons/ic_flashcard_context.svg';

  /// File path: assets/icons/ic_flip_ai_gen.svg
  String get icFlipAiGen => 'assets/icons/ic_flip_ai_gen.svg';

  /// File path: assets/icons/ic_flip_chat.svg
  String get icFlipChat => 'assets/icons/ic_flip_chat.svg';

  /// File path: assets/icons/ic_flip_copy.svg
  String get icFlipCopy => 'assets/icons/ic_flip_copy.svg';

  /// File path: assets/icons/ic_flip_create_note_done.svg
  String get icFlipCreateNoteDone =>
      'assets/icons/ic_flip_create_note_done.svg';

  /// File path: assets/icons/ic_flip_delete_account.svg
  String get icFlipDeleteAccount => 'assets/icons/ic_flip_delete_account.svg';

  /// File path: assets/icons/ic_flip_document_text.svg
  String get icFlipDocumentText => 'assets/icons/ic_flip_document_text.svg';

  /// File path: assets/icons/ic_flip_edit_note.svg
  String get icFlipEditNote => 'assets/icons/ic_flip_edit_note.svg';

  /// File path: assets/icons/ic_flip_folder.svg
  String get icFlipFolder => 'assets/icons/ic_flip_folder.svg';

  /// File path: assets/icons/ic_flip_folder_gray.svg
  String get icFlipFolderGray => 'assets/icons/ic_flip_folder_gray.svg';

  /// File path: assets/icons/ic_flip_folder_mini.svg
  String get icFlipFolderMini => 'assets/icons/ic_flip_folder_mini.svg';

  /// File path: assets/icons/ic_flip_home_youtube.svg
  String get icFlipHomeYoutube => 'assets/icons/ic_flip_home_youtube.svg';

  /// File path: assets/icons/ic_flip_log_out.svg
  String get icFlipLogOut => 'assets/icons/ic_flip_log_out.svg';

  /// File path: assets/icons/ic_flip_play_white.svg
  String get icFlipPlayWhite => 'assets/icons/ic_flip_play_white.svg';

  /// File path: assets/icons/ic_flip_reset.svg
  String get icFlipReset => 'assets/icons/ic_flip_reset.svg';

  /// File path: assets/icons/ic_flip_search_enable.svg
  String get icFlipSearchEnable => 'assets/icons/ic_flip_search_enable.svg';

  /// File path: assets/icons/ic_flip_share2.svg
  String get icFlipShare2 => 'assets/icons/ic_flip_share2.svg';

  /// File path: assets/icons/ic_flip_share_transcript.svg
  String get icFlipShareTranscript =>
      'assets/icons/ic_flip_share_transcript.svg';

  /// File path: assets/icons/ic_flip_shield_tick.svg
  String get icFlipShieldTick => 'assets/icons/ic_flip_shield_tick.svg';

  /// File path: assets/icons/ic_flip_sync_notes.svg
  String get icFlipSyncNotes => 'assets/icons/ic_flip_sync_notes.svg';

  /// File path: assets/icons/ic_flip_tag_setting.svg
  String get icFlipTagSetting => 'assets/icons/ic_flip_tag_setting.svg';

  /// File path: assets/icons/ic_flip_translate.svg
  String get icFlipTranslate => 'assets/icons/ic_flip_translate.svg';

  /// File path: assets/icons/ic_flip_upload_disable.svg
  String get icFlipUploadDisable => 'assets/icons/ic_flip_upload_disable.svg';

  /// File path: assets/icons/ic_flip_upload_enable.svg
  String get icFlipUploadEnable => 'assets/icons/ic_flip_upload_enable.svg';

  /// File path: assets/icons/ic_flip_whats_new_setting.svg
  String get icFlipWhatsNewSetting =>
      'assets/icons/ic_flip_whats_new_setting.svg';

  /// File path: assets/icons/ic_flower.svg
  String get icFlower => 'assets/icons/ic_flower.svg';

  /// File path: assets/icons/ic_folder.svg
  String get icFolder => 'assets/icons/ic_folder.svg';

  /// File path: assets/icons/ic_folder_dropdown_light_mode.svg
  String get icFolderDropdownLightMode =>
      'assets/icons/ic_folder_dropdown_light_mode.svg';

  /// File path: assets/icons/ic_folder_error.svg
  String get icFolderError => 'assets/icons/ic_folder_error.svg';

  /// File path: assets/icons/ic_folder_gray.svg
  String get icFolderGray => 'assets/icons/ic_folder_gray.svg';

  /// File path: assets/icons/ic_folder_item.svg
  String get icFolderItem => 'assets/icons/ic_folder_item.svg';

  /// File path: assets/icons/ic_folder_mini.svg
  String get icFolderMini => 'assets/icons/ic_folder_mini.svg';

  /// File path: assets/icons/ic_folder_more.svg
  String get icFolderMore => 'assets/icons/ic_folder_more.svg';

  /// File path: assets/icons/ic_folder_setting.svg
  String get icFolderSetting => 'assets/icons/ic_folder_setting.svg';

  /// File path: assets/icons/ic_forward.svg
  String get icForward => 'assets/icons/ic_forward.svg';

  /// File path: assets/icons/ic_forward_fast_5.svg
  String get icForwardFast5 => 'assets/icons/ic_forward_fast_5.svg';

  /// File path: assets/icons/ic_forward_slow_5.svg
  String get icForwardSlow5 => 'assets/icons/ic_forward_slow_5.svg';

  /// File path: assets/icons/ic_fullscreen.svg
  String get icFullscreen => 'assets/icons/ic_fullscreen.svg';

  /// File path: assets/icons/ic_fullscreen_exit.svg
  String get icFullscreenExit => 'assets/icons/ic_fullscreen_exit.svg';

  /// File path: assets/icons/ic_gen_ai.svg
  String get icGenAi => 'assets/icons/ic_gen_ai.svg';

  /// File path: assets/icons/ic_gif.svg
  String get icGif => 'assets/icons/ic_gif.svg';

  /// File path: assets/icons/ic_gif_promotion.svg
  String get icGifPromotion => 'assets/icons/ic_gif_promotion.svg';

  /// File path: assets/icons/ic_gift_setting.svg
  String get icGiftSetting => 'assets/icons/ic_gift_setting.svg';

  /// File path: assets/icons/ic_gim.svg
  String get icGim => 'assets/icons/ic_gim.svg';

  /// File path: assets/icons/ic_gim_price.svg
  String get icGimPrice => 'assets/icons/ic_gim_price.svg';

  /// File path: assets/icons/ic_google.svg
  String get icGoogle => 'assets/icons/ic_google.svg';

  /// File path: assets/icons/ic_grid_others.svg
  String get icGridOthers => 'assets/icons/ic_grid_others.svg';

  /// File path: assets/icons/ic_have.svg
  String get icHave => 'assets/icons/ic_have.svg';

  /// File path: assets/icons/ic_header_image_info_dialog.svg
  String get icHeaderImageInfoDialog =>
      'assets/icons/ic_header_image_info_dialog.svg';

  /// File path: assets/icons/ic_history_chat.svg
  String get icHistoryChat => 'assets/icons/ic_history_chat.svg';

  /// File path: assets/icons/ic_history_new.svg
  String get icHistoryNew => 'assets/icons/ic_history_new.svg';

  /// File path: assets/icons/ic_home_alert.svg
  String get icHomeAlert => 'assets/icons/ic_home_alert.svg';

  /// File path: assets/icons/ic_home_atoz.svg
  String get icHomeAtoz => 'assets/icons/ic_home_atoz.svg';

  /// File path: assets/icons/ic_home_audio.svg
  String get icHomeAudio => 'assets/icons/ic_home_audio.svg';

  /// File path: assets/icons/ic_home_disable.svg
  String get icHomeDisable => 'assets/icons/ic_home_disable.svg';

  /// File path: assets/icons/ic_home_doc.svg
  String get icHomeDoc => 'assets/icons/ic_home_doc.svg';

  /// File path: assets/icons/ic_home_document.svg
  String get icHomeDocument => 'assets/icons/ic_home_document.svg';

  /// File path: assets/icons/ic_home_edit.svg
  String get icHomeEdit => 'assets/icons/ic_home_edit.svg';

  /// File path: assets/icons/ic_home_enable.svg
  String get icHomeEnable => 'assets/icons/ic_home_enable.svg';

  /// File path: assets/icons/ic_home_file.svg
  String get icHomeFile => 'assets/icons/ic_home_file.svg';

  /// File path: assets/icons/ic_home_folder.svg
  String get icHomeFolder => 'assets/icons/ic_home_folder.svg';

  /// File path: assets/icons/ic_home_life_time.svg
  String get icHomeLifeTime => 'assets/icons/ic_home_life_time.svg';

  /// File path: assets/icons/ic_home_life_time_pro.svg
  String get icHomeLifeTimePro => 'assets/icons/ic_home_life_time_pro.svg';

  /// File path: assets/icons/ic_home_lifetime_pro.svg
  String get icHomeLifetimePro => 'assets/icons/ic_home_lifetime_pro.svg';

  /// File path: assets/icons/ic_home_no_internet.svg
  String get icHomeNoInternet => 'assets/icons/ic_home_no_internet.svg';

  /// File path: assets/icons/ic_home_record.png
  AssetGenImage get icHomeRecord =>
      const AssetGenImage('assets/icons/ic_home_record.png');

  /// File path: assets/icons/ic_home_record_audio.svg
  String get icHomeRecordAudio => 'assets/icons/ic_home_record_audio.svg';

  /// File path: assets/icons/ic_home_referral.svg
  String get icHomeReferral => 'assets/icons/ic_home_referral.svg';

  /// File path: assets/icons/ic_home_sort.svg
  String get icHomeSort => 'assets/icons/ic_home_sort.svg';

  /// File path: assets/icons/ic_home_text.svg
  String get icHomeText => 'assets/icons/ic_home_text.svg';

  /// File path: assets/icons/ic_home_unlock_pro.svg
  String get icHomeUnlockPro => 'assets/icons/ic_home_unlock_pro.svg';

  /// File path: assets/icons/ic_home_upgrade_pro.svg
  String get icHomeUpgradePro => 'assets/icons/ic_home_upgrade_pro.svg';

  /// File path: assets/icons/ic_home_upload_audio.svg
  String get icHomeUploadAudio => 'assets/icons/ic_home_upload_audio.svg';

  /// File path: assets/icons/ic_home_youtube.svg
  String get icHomeYoutube => 'assets/icons/ic_home_youtube.svg';

  /// File path: assets/icons/ic_home_yt.svg
  String get icHomeYt => 'assets/icons/ic_home_yt.svg';

  /// File path: assets/icons/ic_home_ztoa.svg
  String get icHomeZtoa => 'assets/icons/ic_home_ztoa.svg';

  /// File path: assets/icons/ic_iap_1.svg
  String get icIap1 => 'assets/icons/ic_iap_1.svg';

  /// File path: assets/icons/ic_iap_10.svg
  String get icIap10 => 'assets/icons/ic_iap_10.svg';

  /// File path: assets/icons/ic_iap_11.svg
  String get icIap11 => 'assets/icons/ic_iap_11.svg';

  /// File path: assets/icons/ic_iap_12.svg
  String get icIap12 => 'assets/icons/ic_iap_12.svg';

  /// File path: assets/icons/ic_iap_2.svg
  String get icIap2 => 'assets/icons/ic_iap_2.svg';

  /// File path: assets/icons/ic_iap_3.svg
  String get icIap3 => 'assets/icons/ic_iap_3.svg';

  /// File path: assets/icons/ic_iap_4.svg
  String get icIap4 => 'assets/icons/ic_iap_4.svg';

  /// File path: assets/icons/ic_iap_5.svg
  String get icIap5 => 'assets/icons/ic_iap_5.svg';

  /// File path: assets/icons/ic_iap_6.svg
  String get icIap6 => 'assets/icons/ic_iap_6.svg';

  /// File path: assets/icons/ic_iap_7.svg
  String get icIap7 => 'assets/icons/ic_iap_7.svg';

  /// File path: assets/icons/ic_iap_8.svg
  String get icIap8 => 'assets/icons/ic_iap_8.svg';

  /// File path: assets/icons/ic_iap_9.svg
  String get icIap9 => 'assets/icons/ic_iap_9.svg';

  /// File path: assets/icons/ic_iap_splash_ai.svg
  String get icIapSplashAi => 'assets/icons/ic_iap_splash_ai.svg';

  /// File path: assets/icons/ic_iap_web_import.svg
  String get icIapWebImport => 'assets/icons/ic_iap_web_import.svg';

  /// File path: assets/icons/ic_image.svg
  String get icImage => 'assets/icons/ic_image.svg';

  /// File path: assets/icons/ic_info.svg
  String get icInfo => 'assets/icons/ic_info.svg';

  /// File path: assets/icons/ic_language.svg
  String get icLanguage => 'assets/icons/ic_language.svg';

  /// File path: assets/icons/ic_left_circle.svg
  String get icLeftCircle => 'assets/icons/ic_left_circle.svg';

  /// File path: assets/icons/ic_left_circle_back_light_mode.svg
  String get icLeftCircleBackLightMode =>
      'assets/icons/ic_left_circle_back_light_mode.svg';

  /// File path: assets/icons/ic_left_circle_black.svg
  String get icLeftCircleBlack => 'assets/icons/ic_left_circle_black.svg';

  /// File path: assets/icons/ic_light_setting.svg
  String get icLightSetting => 'assets/icons/ic_light_setting.svg';

  /// File path: assets/icons/ic_line.svg
  String get icLine => 'assets/icons/ic_line.svg';

  /// File path: assets/icons/ic_linkedin.svg
  String get icLinkedin => 'assets/icons/ic_linkedin.svg';

  /// File path: assets/icons/ic_loading.svg
  String get icLoading => 'assets/icons/ic_loading.svg';

  /// File path: assets/icons/ic_login_guide_1.svg
  String get icLoginGuide1 => 'assets/icons/ic_login_guide_1.svg';

  /// File path: assets/icons/ic_login_guide_2.svg
  String get icLoginGuide2 => 'assets/icons/ic_login_guide_2.svg';

  /// File path: assets/icons/ic_login_guide_3.svg
  String get icLoginGuide3 => 'assets/icons/ic_login_guide_3.svg';

  /// File path: assets/icons/ic_login_guide_4.svg
  String get icLoginGuide4 => 'assets/icons/ic_login_guide_4.svg';

  /// File path: assets/icons/ic_logout.svg
  String get icLogout => 'assets/icons/ic_logout.svg';

  /// File path: assets/icons/ic_loud_speaker.svg
  String get icLoudSpeaker => 'assets/icons/ic_loud_speaker.svg';

  /// File path: assets/icons/ic_loud_speaker_mute.svg
  String get icLoudSpeakerMute => 'assets/icons/ic_loud_speaker_mute.svg';

  /// File path: assets/icons/ic_mascot_logout.svg
  String get icMascotLogout => 'assets/icons/ic_mascot_logout.svg';

  /// File path: assets/icons/ic_mascott_delete.svg
  String get icMascottDelete => 'assets/icons/ic_mascott_delete.svg';

  /// File path: assets/icons/ic_mc_conf.svg
  String get icMcConf => 'assets/icons/ic_mc_conf.svg';

  /// File path: assets/icons/ic_mc_delete.svg
  String get icMcDelete => 'assets/icons/ic_mc_delete.svg';

  /// File path: assets/icons/ic_micro_layout_b.svg
  String get icMicroLayoutB => 'assets/icons/ic_micro_layout_b.svg';

  /// File path: assets/icons/ic_mindmap_layout_b.svg
  String get icMindmapLayoutB => 'assets/icons/ic_mindmap_layout_b.svg';

  /// File path: assets/icons/ic_mini_pro.svg
  String get icMiniPro => 'assets/icons/ic_mini_pro.svg';

  /// File path: assets/icons/ic_more.svg
  String get icMore => 'assets/icons/ic_more.svg';

  /// File path: assets/icons/ic_move_folder.svg
  String get icMoveFolder => 'assets/icons/ic_move_folder.svg';

  /// File path: assets/icons/ic_move_folder_detail.svg
  String get icMoveFolderDetail => 'assets/icons/ic_move_folder_detail.svg';

  /// File path: assets/icons/ic_move_to_folder.svg
  String get icMoveToFolder => 'assets/icons/ic_move_to_folder.svg';

  /// File path: assets/icons/ic_multiple_img.svg
  String get icMultipleImg => 'assets/icons/ic_multiple_img.svg';

  /// File path: assets/icons/ic_neutral.svg
  String get icNeutral => 'assets/icons/ic_neutral.svg';

  /// File path: assets/icons/ic_new.svg
  String get icNew => 'assets/icons/ic_new.svg';

  /// File path: assets/icons/ic_none.svg
  String get icNone => 'assets/icons/ic_none.svg';

  /// File path: assets/icons/ic_note2.svg
  String get icNote2 => 'assets/icons/ic_note2.svg';

  /// File path: assets/icons/ic_note_detail_delete.svg
  String get icNoteDetailDelete => 'assets/icons/ic_note_detail_delete.svg';

  /// File path: assets/icons/ic_note_detail_play_.svg
  String get icNoteDetailPlay => 'assets/icons/ic_note_detail_play_.svg';

  /// File path: assets/icons/ic_note_reminder.svg
  String get icNoteReminder => 'assets/icons/ic_note_reminder.svg';

  /// File path: assets/icons/ic_note_sharing_view_copy.svg
  String get icNoteSharingViewCopy =>
      'assets/icons/ic_note_sharing_view_copy.svg';

  /// File path: assets/icons/ic_notex_pro.svg
  String get icNotexPro => 'assets/icons/ic_notex_pro.svg';

  /// File path: assets/icons/ic_number_question.svg
  String get icNumberQuestion => 'assets/icons/ic_number_question.svg';

  /// File path: assets/icons/ic_ob_arrow_left.svg
  String get icObArrowLeft => 'assets/icons/ic_ob_arrow_left.svg';

  /// File path: assets/icons/ic_ob_trans.svg
  String get icObTrans => 'assets/icons/ic_ob_trans.svg';

  /// File path: assets/icons/ic_paint.svg
  String get icPaint => 'assets/icons/ic_paint.svg';

  /// File path: assets/icons/ic_pause.svg
  String get icPause => 'assets/icons/ic_pause.svg';

  /// File path: assets/icons/ic_pen.svg
  String get icPen => 'assets/icons/ic_pen.svg';

  /// File path: assets/icons/ic_pencil.svg
  String get icPencil => 'assets/icons/ic_pencil.svg';

  /// File path: assets/icons/ic_play_audio.svg
  String get icPlayAudio => 'assets/icons/ic_play_audio.svg';

  /// File path: assets/icons/ic_play_circle.svg
  String get icPlayCircle => 'assets/icons/ic_play_circle.svg';

  /// File path: assets/icons/ic_play_left.svg
  String get icPlayLeft => 'assets/icons/ic_play_left.svg';

  /// File path: assets/icons/ic_play_right.svg
  String get icPlayRight => 'assets/icons/ic_play_right.svg';

  /// File path: assets/icons/ic_play_white.svg
  String get icPlayWhite => 'assets/icons/ic_play_white.svg';

  /// File path: assets/icons/ic_plus.svg
  String get icPlus => 'assets/icons/ic_plus.svg';

  /// File path: assets/icons/ic_preview_video.svg
  String get icPreviewVideo => 'assets/icons/ic_preview_video.svg';

  /// File path: assets/icons/ic_pro.svg
  String get icPro => 'assets/icons/ic_pro.svg';

  /// File path: assets/icons/ic_pro_iap.svg
  String get icProIap => 'assets/icons/ic_pro_iap.svg';

  /// File path: assets/icons/ic_pro_layout_b.svg
  String get icProLayoutB => 'assets/icons/ic_pro_layout_b.svg';

  /// File path: assets/icons/ic_pro_select.svg
  String get icProSelect => 'assets/icons/ic_pro_select.svg';

  /// File path: assets/icons/ic_pro_unselect.svg
  String get icProUnselect => 'assets/icons/ic_pro_unselect.svg';

  /// File path: assets/icons/ic_proed.svg
  String get icProed => 'assets/icons/ic_proed.svg';

  /// File path: assets/icons/ic_quiz_score.svg
  String get icQuizScore => 'assets/icons/ic_quiz_score.svg';

  /// File path: assets/icons/ic_quizz.svg
  String get icQuizz => 'assets/icons/ic_quizz.svg';

  /// File path: assets/icons/ic_rate_star_on_store.svg
  String get icRateStarOnStore => 'assets/icons/ic_rate_star_on_store.svg';

  /// File path: assets/icons/ic_record.svg
  String get icRecord => 'assets/icons/ic_record.svg';

  /// File path: assets/icons/ic_record_coll.svg
  String get icRecordColl => 'assets/icons/ic_record_coll.svg';

  /// File path: assets/icons/ic_recording_schedule_clock.svg
  String get icRecordingScheduleClock =>
      'assets/icons/ic_recording_schedule_clock.svg';

  /// File path: assets/icons/ic_referral_setting.svg
  String get icReferralSetting => 'assets/icons/ic_referral_setting.svg';

  /// File path: assets/icons/ic_reload.svg
  String get icReload => 'assets/icons/ic_reload.svg';

  /// File path: assets/icons/ic_reload_white.svg
  String get icReloadWhite => 'assets/icons/ic_reload_white.svg';

  /// File path: assets/icons/ic_reset.svg
  String get icReset => 'assets/icons/ic_reset.svg';

  /// File path: assets/icons/ic_resume_white.svg
  String get icResumeWhite => 'assets/icons/ic_resume_white.svg';

  /// File path: assets/icons/ic_rewind.svg
  String get icRewind => 'assets/icons/ic_rewind.svg';

  /// File path: assets/icons/ic_right_circle.svg
  String get icRightCircle => 'assets/icons/ic_right_circle.svg';

  /// File path: assets/icons/ic_right_circle_black.svg
  String get icRightCircleBlack => 'assets/icons/ic_right_circle_black.svg';

  /// File path: assets/icons/ic_right_circle_light_mode.svg
  String get icRightCircleLightMode =>
      'assets/icons/ic_right_circle_light_mode.svg';

  /// File path: assets/icons/ic_scroll_grid.svg
  String get icScrollGrid => 'assets/icons/ic_scroll_grid.svg';

  /// File path: assets/icons/ic_scroll_horizontal.svg
  String get icScrollHorizontal => 'assets/icons/ic_scroll_horizontal.svg';

  /// File path: assets/icons/ic_scroll_vertical.svg
  String get icScrollVertical => 'assets/icons/ic_scroll_vertical.svg';

  /// File path: assets/icons/ic_search_disable.svg
  String get icSearchDisable => 'assets/icons/ic_search_disable.svg';

  /// File path: assets/icons/ic_search_enable.svg
  String get icSearchEnable => 'assets/icons/ic_search_enable.svg';

  /// File path: assets/icons/ic_search_more.svg
  String get icSearchMore => 'assets/icons/ic_search_more.svg';

  /// File path: assets/icons/ic_select_folder.svg
  String get icSelectFolder => 'assets/icons/ic_select_folder.svg';

  /// File path: assets/icons/ic_select_language.svg
  String get icSelectLanguage => 'assets/icons/ic_select_language.svg';

  /// File path: assets/icons/ic_selected.svg
  String get icSelected => 'assets/icons/ic_selected.svg';

  /// File path: assets/icons/ic_send.svg
  String get icSend => 'assets/icons/ic_send.svg';

  /// File path: assets/icons/ic_send_chat.svg
  String get icSendChat => 'assets/icons/ic_send_chat.svg';

  /// File path: assets/icons/ic_sets.svg
  String get icSets => 'assets/icons/ic_sets.svg';

  /// File path: assets/icons/ic_setting.svg
  String get icSetting => 'assets/icons/ic_setting.svg';

  /// File path: assets/icons/ic_setting_about_us.svg
  String get icSettingAboutUs => 'assets/icons/ic_setting_about_us.svg';

  /// File path: assets/icons/ic_setting_account.svg
  String get icSettingAccount => 'assets/icons/ic_setting_account.svg';

  /// File path: assets/icons/ic_setting_arrow_left.svg
  String get icSettingArrowLeft => 'assets/icons/ic_setting_arrow_left.svg';

  /// File path: assets/icons/ic_setting_arrow_right.svg
  String get icSettingArrowRight => 'assets/icons/ic_setting_arrow_right.svg';

  /// File path: assets/icons/ic_setting_contact_us.svg
  String get icSettingContactUs => 'assets/icons/ic_setting_contact_us.svg';

  /// File path: assets/icons/ic_setting_custom_tab.svg
  String get icSettingCustomTab => 'assets/icons/ic_setting_custom_tab.svg';

  /// File path: assets/icons/ic_setting_dark_mode.svg
  String get icSettingDarkMode => 'assets/icons/ic_setting_dark_mode.svg';

  /// File path: assets/icons/ic_setting_done.svg
  String get icSettingDone => 'assets/icons/ic_setting_done.svg';

  /// File path: assets/icons/ic_setting_suggest_feature.svg
  String get icSettingSuggestFeature =>
      'assets/icons/ic_setting_suggest_feature.svg';

  /// File path: assets/icons/ic_setting_switch_theme.svg
  String get icSettingSwitchTheme => 'assets/icons/ic_setting_switch_theme.svg';

  /// File path: assets/icons/ic_settings_upgrade_version.svg
  String get icSettingsUpgradeVersion =>
      'assets/icons/ic_settings_upgrade_version.svg';

  /// File path: assets/icons/ic_share.svg
  String get icShare => 'assets/icons/ic_share.svg';

  /// File path: assets/icons/ic_share2.svg
  String get icShare2 => 'assets/icons/ic_share2.svg';

  /// File path: assets/icons/ic_share_3.svg
  String get icShare3 => 'assets/icons/ic_share_3.svg';

  /// File path: assets/icons/ic_share_audio.svg
  String get icShareAudio => 'assets/icons/ic_share_audio.svg';

  /// File path: assets/icons/ic_share_note.svg
  String get icShareNote => 'assets/icons/ic_share_note.svg';

  /// File path: assets/icons/ic_share_short.svg
  String get icShareShort => 'assets/icons/ic_share_short.svg';

  /// File path: assets/icons/ic_share_transcript.svg
  String get icShareTranscript => 'assets/icons/ic_share_transcript.svg';

  /// File path: assets/icons/ic_shield_tick.svg
  String get icShieldTick => 'assets/icons/ic_shield_tick.svg';

  /// File path: assets/icons/ic_short_pause.svg
  String get icShortPause => 'assets/icons/ic_short_pause.svg';

  /// File path: assets/icons/ic_short_pause1.svg
  String get icShortPause1 => 'assets/icons/ic_short_pause1.svg';

  /// File path: assets/icons/ic_short_play.svg
  String get icShortPlay => 'assets/icons/ic_short_play.svg';

  /// File path: assets/icons/ic_short_play1.svg
  String get icShortPlay1 => 'assets/icons/ic_short_play1.svg';

  /// File path: assets/icons/ic_short_yt.svg
  String get icShortYt => 'assets/icons/ic_short_yt.svg';

  /// File path: assets/icons/ic_shorts_audio.svg
  String get icShortsAudio => 'assets/icons/ic_shorts_audio.svg';

  /// File path: assets/icons/ic_shorts_change_bg.svg
  String get icShortsChangeBg => 'assets/icons/ic_shorts_change_bg.svg';

  /// File path: assets/icons/ic_shorts_layout_b.svg
  String get icShortsLayoutB => 'assets/icons/ic_shorts_layout_b.svg';

  /// File path: assets/icons/ic_shorts_play.svg
  String get icShortsPlay => 'assets/icons/ic_shorts_play.svg';

  /// File path: assets/icons/ic_shorts_watermark.svg
  String get icShortsWatermark => 'assets/icons/ic_shorts_watermark.svg';

  /// File path: assets/icons/ic_slide.svg
  String get icSlide => 'assets/icons/ic_slide.svg';

  /// File path: assets/icons/ic_slide01.svg
  String get icSlide01 => 'assets/icons/ic_slide01.svg';

  /// File path: assets/icons/ic_smart_lear.svg
  String get icSmartLear => 'assets/icons/ic_smart_lear.svg';

  /// File path: assets/icons/ic_snow_flake.svg
  String get icSnowFlake => 'assets/icons/ic_snow_flake.svg';

  /// File path: assets/icons/ic_star_blue.svg
  String get icStarBlue => 'assets/icons/ic_star_blue.svg';

  /// File path: assets/icons/ic_star_filled.svg
  String get icStarFilled => 'assets/icons/ic_star_filled.svg';

  /// File path: assets/icons/ic_star_gradiant.svg
  String get icStarGradiant => 'assets/icons/ic_star_gradiant.svg';

  /// File path: assets/icons/ic_star_orange.svg
  String get icStarOrange => 'assets/icons/ic_star_orange.svg';

  /// File path: assets/icons/ic_star_outline.svg
  String get icStarOutline => 'assets/icons/ic_star_outline.svg';

  /// File path: assets/icons/ic_star_rate.svg
  String get icStarRate => 'assets/icons/ic_star_rate.svg';

  /// File path: assets/icons/ic_start_second.svg
  String get icStartSecond => 'assets/icons/ic_start_second.svg';

  /// File path: assets/icons/ic_started.svg
  String get icStarted => 'assets/icons/ic_started.svg';

  /// File path: assets/icons/ic_student_select.svg
  String get icStudentSelect => 'assets/icons/ic_student_select.svg';

  /// File path: assets/icons/ic_student_unselect.svg
  String get icStudentUnselect => 'assets/icons/ic_student_unselect.svg';

  /// File path: assets/icons/ic_sync_notes.svg
  String get icSyncNotes => 'assets/icons/ic_sync_notes.svg';

  /// File path: assets/icons/ic_system_setting.svg
  String get icSystemSetting => 'assets/icons/ic_system_setting.svg';

  /// File path: assets/icons/ic_tag_save.svg
  String get icTagSave => 'assets/icons/ic_tag_save.svg';

  /// File path: assets/icons/ic_tag_setting.svg
  String get icTagSetting => 'assets/icons/ic_tag_setting.svg';

  /// File path: assets/icons/ic_take_pictures.svg
  String get icTakePictures => 'assets/icons/ic_take_pictures.svg';

  /// File path: assets/icons/ic_take_pictures_light_mode.svg
  String get icTakePicturesLightMode =>
      'assets/icons/ic_take_pictures_light_mode.svg';

  /// File path: assets/icons/ic_temp_ob.svg
  String get icTempOb => 'assets/icons/ic_temp_ob.svg';

  /// File path: assets/icons/ic_to_do_list.svg
  String get icToDoList => 'assets/icons/ic_to_do_list.svg';

  /// File path: assets/icons/ic_translate.svg
  String get icTranslate => 'assets/icons/ic_translate.svg';

  /// File path: assets/icons/ic_trash_dialog.svg
  String get icTrashDialog => 'assets/icons/ic_trash_dialog.svg';

  /// File path: assets/icons/ic_tweet.svg
  String get icTweet => 'assets/icons/ic_tweet.svg';

  /// File path: assets/icons/ic_un_selected.svg
  String get icUnSelected => 'assets/icons/ic_un_selected.svg';

  /// File path: assets/icons/ic_uncheck_box_folder.svg
  String get icUncheckBoxFolder => 'assets/icons/ic_uncheck_box_folder.svg';

  /// File path: assets/icons/ic_uncheck_ob.svg
  String get icUncheckOb => 'assets/icons/ic_uncheck_ob.svg';

  /// File path: assets/icons/ic_unicon.svg
  String get icUnicon => 'assets/icons/ic_unicon.svg';

  /// File path: assets/icons/ic_unlimited.svg
  String get icUnlimited => 'assets/icons/ic_unlimited.svg';

  /// File path: assets/icons/ic_unlimited_layout_b.svg
  String get icUnlimitedLayoutB => 'assets/icons/ic_unlimited_layout_b.svg';

  /// File path: assets/icons/ic_unlimited_white.svg
  String get icUnlimitedWhite => 'assets/icons/ic_unlimited_white.svg';

  /// File path: assets/icons/ic_unlock_iap.svg
  String get icUnlockIap => 'assets/icons/ic_unlock_iap.svg';

  /// File path: assets/icons/ic_up_down_arrow.svg
  String get icUpDownArrow => 'assets/icons/ic_up_down_arrow.svg';

  /// File path: assets/icons/ic_upload.svg
  String get icUpload => 'assets/icons/ic_upload.svg';

  /// File path: assets/icons/ic_upload_disable.svg
  String get icUploadDisable => 'assets/icons/ic_upload_disable.svg';

  /// File path: assets/icons/ic_upload_enable.svg
  String get icUploadEnable => 'assets/icons/ic_upload_enable.svg';

  /// File path: assets/icons/ic_upload_file.svg
  String get icUploadFile => 'assets/icons/ic_upload_file.svg';

  /// File path: assets/icons/ic_user_voice.svg
  String get icUserVoice => 'assets/icons/ic_user_voice.svg';

  /// File path: assets/icons/ic_video_you.svg
  String get icVideoYou => 'assets/icons/ic_video_you.svg';

  /// File path: assets/icons/ic_web_link.svg
  String get icWebLink => 'assets/icons/ic_web_link.svg';

  /// File path: assets/icons/ic_welcome_gift.svg
  String get icWelcomeGift => 'assets/icons/ic_welcome_gift.svg';

  /// File path: assets/icons/ic_welcome_page_document.svg
  String get icWelcomePageDocument =>
      'assets/icons/ic_welcome_page_document.svg';

  /// File path: assets/icons/ic_welcome_page_hi.svg
  String get icWelcomePageHi => 'assets/icons/ic_welcome_page_hi.svg';

  /// File path: assets/icons/ic_welcome_page_record.svg
  String get icWelcomePageRecord => 'assets/icons/ic_welcome_page_record.svg';

  /// File path: assets/icons/ic_welcome_page_text.svg
  String get icWelcomePageText => 'assets/icons/ic_welcome_page_text.svg';

  /// File path: assets/icons/ic_welcome_page_upload.svg
  String get icWelcomePageUpload => 'assets/icons/ic_welcome_page_upload.svg';

  /// File path: assets/icons/ic_welcome_page_youtube.svg
  String get icWelcomePageYoutube => 'assets/icons/ic_welcome_page_youtube.svg';

  /// File path: assets/icons/ic_whats_new_setting.svg
  String get icWhatsNewSetting => 'assets/icons/ic_whats_new_setting.svg';

  /// File path: assets/icons/ic_wrong.svg
  String get icWrong => 'assets/icons/ic_wrong.svg';

  /// File path: assets/icons/img_quiz_start.png
  AssetGenImage get imgQuizStart =>
      const AssetGenImage('assets/icons/img_quiz_start.png');

  /// File path: assets/icons/logo_custom_tab.svg
  String get logoCustomTab => 'assets/icons/logo_custom_tab.svg';

  /// File path: assets/icons/logo_login.svg
  String get logoLogin => 'assets/icons/logo_login.svg';

  /// File path: assets/icons/type_business.svg
  String get typeBusiness => 'assets/icons/type_business.svg';

  /// File path: assets/icons/type_student.svg
  String get typeStudent => 'assets/icons/type_student.svg';

  /// List of all assets
  List<dynamic> get values => [
        ic5Start,
        icAccountCheckBlue,
        icAccountCheckGray,
        icAccountCheckViolet,
        icAccountCheckYellow,
        icActionItems,
        icAdd,
        icAddFolder,
        icAddImage,
        icAddImageLightMode,
        icAddSpecific,
        icAdditional,
        icAiChat,
        icAiGen,
        icAiStar,
        icAnonymousEye,
        icAppbarArrowRight,
        icApple,
        icAppleWatch,
        icArrowLeft,
        icArrowRight,
        icArrowSetting,
        icArrowUp,
        icArrowUp2,
        icAutoCount,
        icAverageRating,
        icBalanced,
        icBell,
        icBlueEye,
        icBlueTarget,
        icBusiness,
        icCalendar,
        icCalendarEditNote,
        icCamera,
        icCameraMascot,
        icCameraScan,
        icCameraStar,
        icCardCount,
        icChat,
        icChatAppBar,
        icCheckBlack,
        icCheckBlue,
        icCheckCircle,
        icCheckCircle1,
        icCheckCreateNote,
        icCheckGreen,
        icCheckOb,
        icCheckPromotion,
        icCheckRadian,
        icCheckRadianGreen,
        icChooseType,
        icCircleAdd,
        icClose,
        icCloseBlack,
        icCloseCircle,
        icCloseIap,
        icCloseSearch,
        icCloseWhite,
        icCoinCredits,
        icCoinEarned,
        icCoinUsed,
        icCommonDialogError,
        icCommonDialogErrorHeaderImage,
        icCommonInfoTooltip,
        icComponent,
        icComputer,
        icCongratulations,
        icConnectInternet,
        icCopy,
        icCopyChat,
        icCopyReferral,
        icCorrect,
        icCreateFolder,
        icCreateNewFolder,
        icCreateNoteDone,
        icCreateNoteDropDown,
        icCreateNotePause,
        icCreateNoteRecord,
        icCreateSubFolder,
        icCrown,
        icCrownSetting,
        icCup,
        icCustomDoc,
        icCustomFlashcard,
        icCustomPodcast,
        icCustomQuiz,
        icCustomShort,
        icDarkSetting,
        icDatingProfile,
        icDefaultAvatar,
        icDelete,
        icDeleteAccount,
        icDeleteAccoutDialog,
        icDeleteDialog,
        icDiamondAccount,
        icDifficulty,
        icDiscardChanges,
        icDiscord,
        icDocumentText,
        icDotDark,
        icDotLight,
        icDotSmall,
        icDowloadSlide,
        icDownload,
        icDropdown,
        icEditNote,
        icEditQuestion,
        icEditReminderTime,
        icEditReminderTitle,
        icEditTranscript,
        icEmail,
        icEmail2,
        icEmptyFolder,
        icEmptyNoteNewUser,
        icEmptyNoteNotexEmpty,
        icEmptySummary,
        icError,
        icEsentialIap,
        icExpandLess,
        icExpandMore,
        icExport01,
        icFlashCard,
        icFlashcardContext,
        icFlipAiGen,
        icFlipChat,
        icFlipCopy,
        icFlipCreateNoteDone,
        icFlipDeleteAccount,
        icFlipDocumentText,
        icFlipEditNote,
        icFlipFolder,
        icFlipFolderGray,
        icFlipFolderMini,
        icFlipHomeYoutube,
        icFlipLogOut,
        icFlipPlayWhite,
        icFlipReset,
        icFlipSearchEnable,
        icFlipShare2,
        icFlipShareTranscript,
        icFlipShieldTick,
        icFlipSyncNotes,
        icFlipTagSetting,
        icFlipTranslate,
        icFlipUploadDisable,
        icFlipUploadEnable,
        icFlipWhatsNewSetting,
        icFlower,
        icFolder,
        icFolderDropdownLightMode,
        icFolderError,
        icFolderGray,
        icFolderItem,
        icFolderMini,
        icFolderMore,
        icFolderSetting,
        icForward,
        icForwardFast5,
        icForwardSlow5,
        icFullscreen,
        icFullscreenExit,
        icGenAi,
        icGif,
        icGifPromotion,
        icGiftSetting,
        icGim,
        icGimPrice,
        icGoogle,
        icGridOthers,
        icHave,
        icHeaderImageInfoDialog,
        icHistoryChat,
        icHistoryNew,
        icHomeAlert,
        icHomeAtoz,
        icHomeAudio,
        icHomeDisable,
        icHomeDoc,
        icHomeDocument,
        icHomeEdit,
        icHomeEnable,
        icHomeFile,
        icHomeFolder,
        icHomeLifeTime,
        icHomeLifeTimePro,
        icHomeLifetimePro,
        icHomeNoInternet,
        icHomeRecord,
        icHomeRecordAudio,
        icHomeReferral,
        icHomeSort,
        icHomeText,
        icHomeUnlockPro,
        icHomeUpgradePro,
        icHomeUploadAudio,
        icHomeYoutube,
        icHomeYt,
        icHomeZtoa,
        icIap1,
        icIap10,
        icIap11,
        icIap12,
        icIap2,
        icIap3,
        icIap4,
        icIap5,
        icIap6,
        icIap7,
        icIap8,
        icIap9,
        icIapSplashAi,
        icIapWebImport,
        icImage,
        icInfo,
        icLanguage,
        icLeftCircle,
        icLeftCircleBackLightMode,
        icLeftCircleBlack,
        icLightSetting,
        icLine,
        icLinkedin,
        icLoading,
        icLoginGuide1,
        icLoginGuide2,
        icLoginGuide3,
        icLoginGuide4,
        icLogout,
        icLoudSpeaker,
        icLoudSpeakerMute,
        icMascotLogout,
        icMascottDelete,
        icMcConf,
        icMcDelete,
        icMicroLayoutB,
        icMindmapLayoutB,
        icMiniPro,
        icMore,
        icMoveFolder,
        icMoveFolderDetail,
        icMoveToFolder,
        icMultipleImg,
        icNeutral,
        icNew,
        icNone,
        icNote2,
        icNoteDetailDelete,
        icNoteDetailPlay,
        icNoteReminder,
        icNoteSharingViewCopy,
        icNotexPro,
        icNumberQuestion,
        icObArrowLeft,
        icObTrans,
        icPaint,
        icPause,
        icPen,
        icPencil,
        icPlayAudio,
        icPlayCircle,
        icPlayLeft,
        icPlayRight,
        icPlayWhite,
        icPlus,
        icPreviewVideo,
        icPro,
        icProIap,
        icProLayoutB,
        icProSelect,
        icProUnselect,
        icProed,
        icQuizScore,
        icQuizz,
        icRateStarOnStore,
        icRecord,
        icRecordColl,
        icRecordingScheduleClock,
        icReferralSetting,
        icReload,
        icReloadWhite,
        icReset,
        icResumeWhite,
        icRewind,
        icRightCircle,
        icRightCircleBlack,
        icRightCircleLightMode,
        icScrollGrid,
        icScrollHorizontal,
        icScrollVertical,
        icSearchDisable,
        icSearchEnable,
        icSearchMore,
        icSelectFolder,
        icSelectLanguage,
        icSelected,
        icSend,
        icSendChat,
        icSets,
        icSetting,
        icSettingAboutUs,
        icSettingAccount,
        icSettingArrowLeft,
        icSettingArrowRight,
        icSettingContactUs,
        icSettingCustomTab,
        icSettingDarkMode,
        icSettingDone,
        icSettingSuggestFeature,
        icSettingSwitchTheme,
        icSettingsUpgradeVersion,
        icShare,
        icShare2,
        icShare3,
        icShareAudio,
        icShareNote,
        icShareShort,
        icShareTranscript,
        icShieldTick,
        icShortPause,
        icShortPause1,
        icShortPlay,
        icShortPlay1,
        icShortYt,
        icShortsAudio,
        icShortsChangeBg,
        icShortsLayoutB,
        icShortsPlay,
        icShortsWatermark,
        icSlide,
        icSlide01,
        icSmartLear,
        icSnowFlake,
        icStarBlue,
        icStarFilled,
        icStarGradiant,
        icStarOrange,
        icStarOutline,
        icStarRate,
        icStartSecond,
        icStarted,
        icStudentSelect,
        icStudentUnselect,
        icSyncNotes,
        icSystemSetting,
        icTagSave,
        icTagSetting,
        icTakePictures,
        icTakePicturesLightMode,
        icTempOb,
        icToDoList,
        icTranslate,
        icTrashDialog,
        icTweet,
        icUnSelected,
        icUncheckBoxFolder,
        icUncheckOb,
        icUnicon,
        icUnlimited,
        icUnlimitedLayoutB,
        icUnlimitedWhite,
        icUnlockIap,
        icUpDownArrow,
        icUpload,
        icUploadDisable,
        icUploadEnable,
        icUploadFile,
        icUserVoice,
        icVideoYou,
        icWebLink,
        icWelcomeGift,
        icWelcomePageDocument,
        icWelcomePageHi,
        icWelcomePageRecord,
        icWelcomePageText,
        icWelcomePageUpload,
        icWelcomePageYoutube,
        icWhatsNewSetting,
        icWrong,
        imgQuizStart,
        logoCustomTab,
        logoLogin,
        typeBusiness,
        typeStudent
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/bg_blur_ob.png
  AssetGenImage get bgBlurOb =>
      const AssetGenImage('assets/images/bg_blur_ob.png');

  /// File path: assets/images/bgr_blur_iap.png
  AssetGenImage get bgrBlurIap =>
      const AssetGenImage('assets/images/bgr_blur_iap.png');

  /// File path: assets/images/bgr_blur_iap_portrait.png
  AssetGenImage get bgrBlurIapPortrait =>
      const AssetGenImage('assets/images/bgr_blur_iap_portrait.png');

  /// File path: assets/images/bgr_blur_ob1.png
  AssetGenImage get bgrBlurOb1 =>
      const AssetGenImage('assets/images/bgr_blur_ob1.png');

  /// File path: assets/images/ic_quiz_answer_context.png
  AssetGenImage get icQuizAnswerContext =>
      const AssetGenImage('assets/images/ic_quiz_answer_context.png');

  /// File path: assets/images/img_bg_chat.png
  AssetGenImage get imgBgChat =>
      const AssetGenImage('assets/images/img_bg_chat.png');

  /// File path: assets/images/img_bg_setting.svg
  String get imgBgSetting => 'assets/images/img_bg_setting.svg';

  /// File path: assets/images/img_bg_welcome_page.png
  AssetGenImage get imgBgWelcomePage =>
      const AssetGenImage('assets/images/img_bg_welcome_page.png');

  /// File path: assets/images/img_bg_welcome_page_ipad.png
  AssetGenImage get imgBgWelcomePageIpad =>
      const AssetGenImage('assets/images/img_bg_welcome_page_ipad.png');

  /// File path: assets/images/img_bgr_iap.png
  AssetGenImage get imgBgrIap =>
      const AssetGenImage('assets/images/img_bgr_iap.png');

  /// File path: assets/images/img_bgr_iap_ipad.png
  AssetGenImage get imgBgrIapIpad =>
      const AssetGenImage('assets/images/img_bgr_iap_ipad.png');

  /// File path: assets/images/img_bgr_iap_landscape.png
  AssetGenImage get imgBgrIapLandscape =>
      const AssetGenImage('assets/images/img_bgr_iap_landscape.png');

  /// File path: assets/images/img_bgr_promotion_quarter.png
  AssetGenImage get imgBgrPromotionQuarter =>
      const AssetGenImage('assets/images/img_bgr_promotion_quarter.png');

  /// File path: assets/images/img_bgr_refer.png
  AssetGenImage get imgBgrRefer =>
      const AssetGenImage('assets/images/img_bgr_refer.png');

  /// File path: assets/images/img_black_fridayv2.png
  AssetGenImage get imgBlackFridayv2 =>
      const AssetGenImage('assets/images/img_black_fridayv2.png');

  /// File path: assets/images/img_blur_thumb.png
  AssetGenImage get imgBlurThumb =>
      const AssetGenImage('assets/images/img_blur_thumb.png');

  /// File path: assets/images/img_detail_congratulation.svg
  String get imgDetailCongratulation =>
      'assets/images/img_detail_congratulation.svg';

  /// File path: assets/images/img_detail_flashcard.svg
  String get imgDetailFlashcard => 'assets/images/img_detail_flashcard.svg';

  /// File path: assets/images/img_detail_mindmap.svg
  String get imgDetailMindmap => 'assets/images/img_detail_mindmap.svg';

  /// File path: assets/images/img_detail_podcast.svg
  String get imgDetailPodcast => 'assets/images/img_detail_podcast.svg';

  /// File path: assets/images/img_detail_quiz.svg
  String get imgDetailQuiz => 'assets/images/img_detail_quiz.svg';

  /// File path: assets/images/img_detail_short.svg
  String get imgDetailShort => 'assets/images/img_detail_short.svg';

  /// File path: assets/images/img_dialog_iap.svg
  String get imgDialogIap => 'assets/images/img_dialog_iap.svg';

  /// File path: assets/images/img_diamond_free.png
  AssetGenImage get imgDiamondFree =>
      const AssetGenImage('assets/images/img_diamond_free.png');

  /// File path: assets/images/img_diamond_pro.png
  AssetGenImage get imgDiamondPro =>
      const AssetGenImage('assets/images/img_diamond_pro.png');

  /// File path: assets/images/img_empty_mascot.svg
  String get imgEmptyMascot => 'assets/images/img_empty_mascot.svg';

  /// File path: assets/images/img_essential_lifetime.png
  AssetGenImage get imgEssentialLifetime =>
      const AssetGenImage('assets/images/img_essential_lifetime.png');

  /// File path: assets/images/img_firework.png
  AssetGenImage get imgFirework =>
      const AssetGenImage('assets/images/img_firework.png');

  /// File path: assets/images/img_flashcard_start.svg
  String get imgFlashcardStart => 'assets/images/img_flashcard_start.svg';

  /// File path: assets/images/img_flip_diamond_free.png
  AssetGenImage get imgFlipDiamondFree =>
      const AssetGenImage('assets/images/img_flip_diamond_free.png');

  /// File path: assets/images/img_flip_diamond_pro.png
  AssetGenImage get imgFlipDiamondPro =>
      const AssetGenImage('assets/images/img_flip_diamond_pro.png');

  /// File path: assets/images/img_flip_essential_lifetime.png
  AssetGenImage get imgFlipEssentialLifetime =>
      const AssetGenImage('assets/images/img_flip_essential_lifetime.png');

  /// File path: assets/images/img_flip_recording.png
  AssetGenImage get imgFlipRecording =>
      const AssetGenImage('assets/images/img_flip_recording.png');

  /// File path: assets/images/img_flip_setting_youre_pro.png
  AssetGenImage get imgFlipSettingYourePro =>
      const AssetGenImage('assets/images/img_flip_setting_youre_pro.png');

  /// File path: assets/images/img_flip_update_pro.png
  AssetGenImage get imgFlipUpdatePro =>
      const AssetGenImage('assets/images/img_flip_update_pro.png');

  /// File path: assets/images/img_frame_black_friday.svg
  String get imgFrameBlackFriday => 'assets/images/img_frame_black_friday.svg';

  /// File path: assets/images/img_frame_iap_lifetime.png
  AssetGenImage get imgFrameIapLifetime =>
      const AssetGenImage('assets/images/img_frame_iap_lifetime.png');

  /// File path: assets/images/img_gif_promotion.png
  AssetGenImage get imgGifPromotion =>
      const AssetGenImage('assets/images/img_gif_promotion.png');

  /// File path: assets/images/img_gif_promotion_ipad.png
  AssetGenImage get imgGifPromotionIpad =>
      const AssetGenImage('assets/images/img_gif_promotion_ipad.png');

  /// File path: assets/images/img_gift_setting.svg
  String get imgGiftSetting => 'assets/images/img_gift_setting.svg';

  /// File path: assets/images/img_headbox_iap.svg
  String get imgHeadboxIap => 'assets/images/img_headbox_iap.svg';

  /// File path: assets/images/img_headbox_iap_green.svg
  String get imgHeadboxIapGreen => 'assets/images/img_headbox_iap_green.svg';

  /// File path: assets/images/img_lifetime_iap.svg
  String get imgLifetimeIap => 'assets/images/img_lifetime_iap.svg';

  /// File path: assets/images/img_mascot.svg
  String get imgMascot => 'assets/images/img_mascot.svg';

  /// File path: assets/images/img_mascot_document.svg
  String get imgMascotDocument => 'assets/images/img_mascot_document.svg';

  /// File path: assets/images/img_mascot_error_slide_show.svg
  String get imgMascotErrorSlideShow =>
      'assets/images/img_mascot_error_slide_show.svg';

  /// File path: assets/images/img_mascot_iap.svg
  String get imgMascotIap => 'assets/images/img_mascot_iap.svg';

  /// File path: assets/images/img_mascot_slide_show.svg
  String get imgMascotSlideShow => 'assets/images/img_mascot_slide_show.svg';

  /// File path: assets/images/img_material_gift.svg
  String get imgMaterialGift => 'assets/images/img_material_gift.svg';

  /// File path: assets/images/img_mindmap_start.svg
  String get imgMindmapStart => 'assets/images/img_mindmap_start.svg';

  /// File path: assets/images/img_podcast.svg
  String get imgPodcast => 'assets/images/img_podcast.svg';

  /// File path: assets/images/img_quiz_congratulation.svg
  String get imgQuizCongratulation =>
      'assets/images/img_quiz_congratulation.svg';

  /// File path: assets/images/img_quiz_start.svg
  String get imgQuizStart => 'assets/images/img_quiz_start.svg';

  /// File path: assets/images/img_recording.png
  AssetGenImage get imgRecording =>
      const AssetGenImage('assets/images/img_recording.png');

  /// File path: assets/images/img_setting_youre_pro.png
  AssetGenImage get imgSettingYourePro =>
      const AssetGenImage('assets/images/img_setting_youre_pro.png');

  /// File path: assets/images/img_shorts_video.svg
  String get imgShortsVideo => 'assets/images/img_shorts_video.svg';

  /// File path: assets/images/img_sound.png
  AssetGenImage get imgSound =>
      const AssetGenImage('assets/images/img_sound.png');

  /// File path: assets/images/img_sound_light_mode.png
  AssetGenImage get imgSoundLightMode =>
      const AssetGenImage('assets/images/img_sound_light_mode.png');

  /// File path: assets/images/img_splash.png
  AssetGenImage get imgSplash =>
      const AssetGenImage('assets/images/img_splash.png');

  /// File path: assets/images/img_tag_new.svg
  String get imgTagNew => 'assets/images/img_tag_new.svg';

  /// File path: assets/images/img_update_pro.png
  AssetGenImage get imgUpdatePro =>
      const AssetGenImage('assets/images/img_update_pro.png');

  /// File path: assets/images/setting_banner_essential.png
  AssetGenImage get settingBannerEssential =>
      const AssetGenImage('assets/images/setting_banner_essential.png');

  /// File path: assets/images/setting_banner_essential_ipad.png
  AssetGenImage get settingBannerEssentialIpad =>
      const AssetGenImage('assets/images/setting_banner_essential_ipad.png');

  /// File path: assets/images/setting_banner_pro.png
  AssetGenImage get settingBannerPro =>
      const AssetGenImage('assets/images/setting_banner_pro.png');

  /// File path: assets/images/setting_banner_pro_ipad.png
  AssetGenImage get settingBannerProIpad =>
      const AssetGenImage('assets/images/setting_banner_pro_ipad.png');

  /// List of all assets
  List<dynamic> get values => [
        bgBlurOb,
        bgrBlurIap,
        bgrBlurIapPortrait,
        bgrBlurOb1,
        icQuizAnswerContext,
        imgBgChat,
        imgBgSetting,
        imgBgWelcomePage,
        imgBgWelcomePageIpad,
        imgBgrIap,
        imgBgrIapIpad,
        imgBgrIapLandscape,
        imgBgrPromotionQuarter,
        imgBgrRefer,
        imgBlackFridayv2,
        imgBlurThumb,
        imgDetailCongratulation,
        imgDetailFlashcard,
        imgDetailMindmap,
        imgDetailPodcast,
        imgDetailQuiz,
        imgDetailShort,
        imgDialogIap,
        imgDiamondFree,
        imgDiamondPro,
        imgEmptyMascot,
        imgEssentialLifetime,
        imgFirework,
        imgFlashcardStart,
        imgFlipDiamondFree,
        imgFlipDiamondPro,
        imgFlipEssentialLifetime,
        imgFlipRecording,
        imgFlipSettingYourePro,
        imgFlipUpdatePro,
        imgFrameBlackFriday,
        imgFrameIapLifetime,
        imgGifPromotion,
        imgGifPromotionIpad,
        imgGiftSetting,
        imgHeadboxIap,
        imgHeadboxIapGreen,
        imgLifetimeIap,
        imgMascot,
        imgMascotDocument,
        imgMascotErrorSlideShow,
        imgMascotIap,
        imgMascotSlideShow,
        imgMaterialGift,
        imgMindmapStart,
        imgPodcast,
        imgQuizCongratulation,
        imgQuizStart,
        imgRecording,
        imgSettingYourePro,
        imgShortsVideo,
        imgSound,
        imgSoundLightMode,
        imgSplash,
        imgTagNew,
        imgUpdatePro,
        settingBannerEssential,
        settingBannerEssentialIpad,
        settingBannerPro,
        settingBannerProIpad
      ];
}

class $AssetsVideosGen {
  const $AssetsVideosGen();

  /// File path: assets/videos/ai_chat_main.json
  String get aiChatMain => 'assets/videos/ai_chat_main.json';

  /// File path: assets/videos/animation_splash_dark.json
  String get animationSplashDark => 'assets/videos/animation_splash_dark.json';

  /// File path: assets/videos/animation_splash_light.json
  String get animationSplashLight =>
      'assets/videos/animation_splash_light.json';

  /// File path: assets/videos/avatar_nova.json
  String get avatarNova => 'assets/videos/avatar_nova.json';

  /// File path: assets/videos/background_video_widget.dart
  String get backgroundVideoWidget =>
      'assets/videos/background_video_widget.dart';

  /// File path: assets/videos/business_onb3.json
  String get businessOnb3 => 'assets/videos/business_onb3.json';

  /// File path: assets/videos/business_onb4.mp4
  String get businessOnb4 => 'assets/videos/business_onb4.mp4';

  /// File path: assets/videos/common_loading.json
  String get commonLoading => 'assets/videos/common_loading.json';

  /// File path: assets/videos/detail_answer_false.json
  String get detailAnswerFalse => 'assets/videos/detail_answer_false.json';

  /// File path: assets/videos/detail_answer_flashcard.json
  String get detailAnswerFlashcard =>
      'assets/videos/detail_answer_flashcard.json';

  /// File path: assets/videos/detail_answer_true.json
  String get detailAnswerTrue => 'assets/videos/detail_answer_true.json';

  /// File path: assets/videos/detail_question.json
  String get detailQuestion => 'assets/videos/detail_question.json';

  /// File path: assets/videos/gif_black_friday.json
  String get gifBlackFriday => 'assets/videos/gif_black_friday.json';

  /// File path: assets/videos/gift_upgrade.json
  String get giftUpgrade => 'assets/videos/gift_upgrade.json';

  /// File path: assets/videos/iap_btn.json
  String get iapBtn => 'assets/videos/iap_btn.json';

  /// File path: assets/videos/ob1.json
  String get ob1 => 'assets/videos/ob1.json';

  /// File path: assets/videos/onb1.json
  String get onb1 => 'assets/videos/onb1.json';

  /// File path: assets/videos/onb2.json
  String get onb2 => 'assets/videos/onb2.json';

  /// File path: assets/videos/onb3_student.json
  String get onb3Student => 'assets/videos/onb3_student.json';

  /// File path: assets/videos/onb4_student.json
  String get onb4Student => 'assets/videos/onb4_student.json';

  /// File path: assets/videos/onb5_student.mp4
  String get onb5Student => 'assets/videos/onb5_student.mp4';

  /// File path: assets/videos/onb6_student_business.json
  String get onb6StudentBusiness => 'assets/videos/onb6_student_business.json';

  /// File path: assets/videos/paywall_main.json
  String get paywallMain => 'assets/videos/paywall_main.json';

  /// File path: assets/videos/podcast_audio_generating.json
  String get podcastAudioGenerating =>
      'assets/videos/podcast_audio_generating.json';

  /// File path: assets/videos/rate_us.json
  String get rateUs => 'assets/videos/rate_us.json';

  /// File path: assets/videos/record_home.json
  String get recordHome => 'assets/videos/record_home.json';

  /// File path: assets/videos/sale_off.json
  String get saleOff => 'assets/videos/sale_off.json';

  /// File path: assets/videos/shorts_creating.json
  String get shortsCreating => 'assets/videos/shorts_creating.json';

  /// File path: assets/videos/star_generate.json
  String get starGenerate => 'assets/videos/star_generate.json';

  /// File path: assets/videos/swap_image.json
  String get swapImage => 'assets/videos/swap_image.json';

  /// File path: assets/videos/type_chat.json
  String get typeChat => 'assets/videos/type_chat.json';

  /// File path: assets/videos/welcome_page_firework.json
  String get welcomePageFirework => 'assets/videos/welcome_page_firework.json';

  /// File path: assets/videos/welcome_page_hand_hi.json
  String get welcomePageHandHi => 'assets/videos/welcome_page_hand_hi.json';

  /// File path: assets/videos/welcome_page_record.json
  String get welcomePageRecord => 'assets/videos/welcome_page_record.json';

  /// List of all assets
  List<String> get values => [
        aiChatMain,
        animationSplashDark,
        animationSplashLight,
        avatarNova,
        backgroundVideoWidget,
        businessOnb3,
        businessOnb4,
        commonLoading,
        detailAnswerFalse,
        detailAnswerFlashcard,
        detailAnswerTrue,
        detailQuestion,
        gifBlackFriday,
        giftUpgrade,
        iapBtn,
        ob1,
        onb1,
        onb2,
        onb3Student,
        onb4Student,
        onb5Student,
        onb6StudentBusiness,
        paywallMain,
        podcastAudioGenerating,
        rateUs,
        recordHome,
        saleOff,
        shortsCreating,
        starGenerate,
        swapImage,
        typeChat,
        welcomePageFirework,
        welcomePageHandHi,
        welcomePageRecord
      ];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsVideosGen videos = $AssetsVideosGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
