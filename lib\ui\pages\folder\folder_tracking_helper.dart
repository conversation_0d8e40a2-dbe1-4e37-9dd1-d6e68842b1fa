import 'package:note_x/utils/analytics_service.dart';
import 'package:note_x/utils/constants/event_name_tracking.dart';

class FolderTrackingHelper {
  static void onCreateFolderTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_create_folder,
    );
  }

  static void onCreateFolderCancel() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_create_folder_cancel,
    );
  }

  static void onUnsyncedNoteTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_unsynced_note,
    );
  }

  static void onFolderDetailTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail,
    );
  }

  static void onMorePressed() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_more_pressed,
    );
  }

  static void onEditFolderTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_edit_folder,
    );
  }

  static void onEditFolderCancel() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_edit_folder_cancel,
    );
  }

  static void onDeleteFolderTap() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_delete_folder,
    );
  }

  static void onDeleteFolderCheckBoxChanged(bool? checked) {
    final eventName = checked == true
        ? EventName.folder_detail_delete_folder_checkbox_checked
        : EventName.folder_detail_delete_folder_checkbox_unchecked;
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: eventName,
    );
  }

  static void onDeleteFolderCancel() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_delete_folder_cancel,
    );
  }

  static void onFolderDetailBack() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_back,
    );
  }
}
