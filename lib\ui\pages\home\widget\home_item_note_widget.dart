import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';

/// A widget that displays a regular note item in the home screen.
class HomeItemNoteWidget extends StatelessWidget {
  final NoteModel noteModel;
  final Function()? onTap;
  final Function()? onLongPress;
  final Widget? overflowMenu;
  final bool isMultiSelectMode;
  final bool isSelected;
  final Function()? onSelectionTap;

  const HomeItemNoteWidget({
    super.key,
    required this.noteModel,
    this.onTap,
    this.onLongPress,
    this.overflowMenu,
    this.isMultiSelectMode = false,
    this.isSelected = false,
    this.onSelectionTap,
  });

  @override
  Widget build(BuildContext context) {
    return noteModel.noteStatus == NoteStatus.loading
        ? _buildCreatingNoteWidget(context)
        : _buildNoteWidget(context);
  }

  Widget _buildCreatingNoteWidget(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: context.isLandscape ? 12 : 12.h,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: const LinearGradient(
            colors: AppColors.gradientCTABlue,
            begin: Alignment.centerRight,
            end: Alignment.centerLeft,
          ),
        ),
        child: Row(
          children: [
            Lottie.asset(
              Assets.videos.shortsCreating,
              width: context.isTablet ? 36 : 40.w,
              height: context.isTablet ? 36 : 40.w,
            ),
            SizedBox(width: context.isTablet ? 8 : 16.w),
            AnimatedTextEffect(
              text: noteModel.currentStep.stepName,
              style: TextStyle(
                color: AppColors.white,
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoteWidget(BuildContext context) {
    return GestureDetector(
      onTap: isMultiSelectMode ? onSelectionTap : onTap,
      onLongPress: onLongPress,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected && isMultiSelectMode
              ? context.colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16.r),
          border: isSelected && isMultiSelectMode
              ? Border.all(
                  color: context.colorScheme.primary.withOpacity(0.3),
                  width: 1,
                )
              : null,
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: 12.h,
            horizontal: 16.w,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: context.colorScheme.mainNeutral,
          ),
          child: Stack(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Checkbox for multi-select mode
                  if (isMultiSelectMode)
                    Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      child: GestureDetector(
                        onTap: onSelectionTap,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: context.colorScheme.mainBlue,
                              width: 2,
                            ),
                            color: isSelected
                                ? context.colorScheme.mainBlue
                                : Colors.transparent,
                          ),
                          child: isSelected
                              ? SvgPicture.asset(
                                  Assets.icons.icCheckBlue,
                                  colorFilter: ColorFilter.mode(context.colorScheme.themeWhite, BlendMode.srcIn),
                                )
                              : null,
                        ),
                      ),
                    ),

                  // Note content
                  Expanded(
                    child: _buildNoteContent(context),
                  ),
                ],
              ),

              // Overflow menu (hidden in multi-select mode)
              if (!isMultiSelectMode)
                Positioned(
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: overflowMenu ?? const SizedBox.shrink(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoteContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Padding(
          padding: EdgeInsets.only(right: isMultiSelectMode ? 0 : 24.w),
          child: DirectionalText(
            text: noteModel.title,
            detectFromContent: true,
            style: TextStyle(
              color: context.colorScheme.mainPrimary,
              fontWeight: FontWeight.w500,
              fontSize: context.isTablet ? 16 : 14.sp,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        // Date and time
        SizedBox(height: context.isTablet ? 4 : 4.h),
        CommonText(
          buildDateTimeAndDurationText(noteModel),
          style: TextStyle(
            fontSize: context.isTablet ? 14 : 12.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray.withOpacity(0.6),
          ),
        ),

        // Tags row
        SizedBox(height: context.isTablet ? 8 : 8.h),
        _buildTagsRow(context),
      ],
    );
  }

  Widget _buildTagsRow(BuildContext context) {
    return Row(
      children: [
        // Note type tag
        _buildNoteTypeTag(context),

        SizedBox(width: 8.w),

        // Folder tag (if available)
        if (noteModel.folderName.isNotEmpty) _buildFolderTag(context),
      ],
    );
  }

  Widget _buildNoteTypeTag(BuildContext context) {
    return Container(
      height: 24.h,
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      decoration: BoxDecoration(
        color: getColorByNoteTypeContainer(context, noteModel),
        borderRadius: BorderRadius.circular(100.r),
      ),
      alignment: Alignment.center,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            height: 16.h,
            width: 16.w,
            getIconByNoteTypeContainer(noteModel),
            colorFilter: ColorFilter.mode(
              getColorByNoteType(context, noteModel),
              BlendMode.srcIn,
            ),
          ),
          SizedBox(width: context.isTablet ? 4 : 4.w),
          Flexible(
            fit: FlexFit.loose,
            child: Container(
              constraints: BoxConstraints(maxWidth: 120.w),
              child: CommonText(
                buildTextTypeOfNote(noteModel),
                style: TextStyle(
                  height: 1,
                  fontWeight: FontWeight.w400,
                  fontSize: context.isTablet ? 14 : 12.sp,
                  color: getColorByNoteType(context, noteModel),
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFolderTag(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      height: context.isLandscape ? 24 * 1.4 : 24.h,
      decoration: BoxDecoration(
        color: context.colorScheme.mainSecondary,
        borderRadius: BorderRadius.circular(100.r),
      ),
      alignment: Alignment.center,
      child: Row(
        children: [
          SvgPicture.asset(
            width: 11.w,
            height: 11.h,
            Assets.icons.icHomeFolder,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainGray,
              BlendMode.srcIn,
            ),
          ),
          SizedBox(width: context.isTablet ? 4 : 4.w),
          CommonText(
            height: 1,
            _truncateFolderName(noteModel.folderName),
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: context.isTablet ? 14 : 12.sp,
              color: context.colorScheme.mainGray,
            ),
          )
        ],
      ),
    );
  }

  String _truncateFolderName(String folderName) {
    return folderName.length > 20
        ? '${folderName.substring(0, 20)}...'
        : folderName;
  }
}
