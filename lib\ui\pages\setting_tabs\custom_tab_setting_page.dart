import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

import '../../../base/base_page_state.dart';
import 'cubit/custom_tab_setting_state.dart';

class CustomTabSettingPage extends StatefulWidget {
  const CustomTabSettingPage({super.key});

  @override
  State<CustomTabSettingPage> createState() => _CustomTabSettingPageState();
}

class _CustomTabSettingPageState
    extends BasePageStateDelegate<CustomTabSettingPage, CustomTabSettingCubit> {
  final List<TabType> allTabs = TabType.values.toList();
  final List<TabType> alwaysSelected = [TabType.summary];

  @override
  void initState() {
    super.initState();
    _initTabs();

    // Add screen view and show events
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.customTabSettingShow,
      );
      AnalyticsService.logEventScreenView(
        screenClass: EventScreenClass.customTabSetting,
        screenName: EventScreenName.customTabSetting,
      );
    });
  }

  Future<void> _initTabs() async {
    await cubit.initTabs(allTabs, alwaysSelected);
  }

  // Function to track saved items
  void _trackSavedTabItems(List<TabType> selectedItems, String screenName) {
    // Skip tracking summary tab as it's always selected
    for (final tabType in selectedItems) {
      if (tabType != TabType.summary) {
        final eventName = _getEventNameForTabType(tabType, screenName);
        AnalyticsService.logAnalyticsEventNoParam(eventName: eventName);
      }
    }
  }

  String _getEventNameForTabType(TabType tabType, String screenName) {
    // Format: scr_tabType_Click
    final tabTypeString = tabType.toString().split('.').last;
    return "${screenName}_${_capitalizeFirstLetter(tabTypeString)}_Click";
  }

  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<CustomTabSettingCubit, CustomTabSettingState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: context.colorScheme.mainBackground,
          appBar: AppBarWidget(
            customTitle: Text(
              S.current.customize_note_tabs,
              style: TextStyle(
                fontSize: context.isTablet ? 17 : 15.sp,
                fontWeight: FontWeight.w700,
                color: context.colorScheme.mainPrimary,
              ),
            ),
            backgroundColor: context.colorScheme.mainBackground,
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonText(
                    S.current.note_tabs,
                    style: TextStyle(
                      color: context.colorScheme.mainGray,
                      fontSize: context.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  AppConstants.kSpacingItem16,
                  SizedBox(
                    height: 48,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: state.selectedTabs.map((tab) {
                        return Padding(
                          padding: EdgeInsets.only(right: 8.w),
                          child: GestureDetector(
                            onTap: () {
                              cubit.removeTab(tab);
                            },
                            child: SizedBox(
                              height: 40,
                              child: Stack(
                                clipBehavior: Clip.none,
                                // Quan trọng để thấy icon tràn
                                alignment: Alignment.centerLeft,
                                children: [
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 16.w,
                                      vertical: 6.h,
                                    ),
                                    decoration: BoxDecoration(
                                      color: context.colorScheme.mainNeutral,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: CommonText(
                                      tab.displayName,
                                      style: TextStyle(
                                        color: context.colorScheme.mainPrimary,
                                        fontSize: context.isTablet ? 16 : 14.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  if (!state.alwaysSelected.contains(tab))
                                    Positioned(
                                      right: -4,
                                      top: 0,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color:
                                                  Colors.black.withOpacity(0.2),
                                              spreadRadius: 1,
                                              blurRadius: 1,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: CircleAvatar(
                                          radius: 10.r,
                                          backgroundColor:
                                              context.colorScheme.mainSecondary,
                                          child: Icon(
                                            Icons.close,
                                            size: 12,
                                            color:
                                                context.colorScheme.mainPrimary,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  AppConstants.kSpacingItem16,
                  Divider(
                    color: context.colorScheme.mainNeutral,
                    thickness: 0.5,
                  ),
                  SizedBox(height: 14.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          CommonText(
                            S.current.all_tabs,
                            style: TextStyle(
                              color: context.colorScheme.mainPrimary,
                              fontSize: context.isTablet ? 16 : 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          AppConstants.kSpacingItemW6,
                          ElTooltip(
                            padding: EdgeInsets.all(8.w),
                            content: Container(
                              constraints: BoxConstraints(
                                maxWidth: 250.w,
                                maxHeight: 78.h,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Transform.scale(
                                    scale: 1.5,
                                    child: Text(
                                      '•',
                                      style: TextStyle(
                                        fontSize: context.isTablet ? 14 : 12.sp,
                                        fontWeight: FontWeight.w400,
                                        color: context.colorScheme.themeWhite,
                                      ),
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW8,
                                  Expanded(
                                    child: Text(
                                      S.current.select_and_reorder,
                                      style: TextStyle(
                                        fontSize: context.isTablet ? 14 : 12.sp,
                                        fontWeight: FontWeight.w400,
                                        color: context.colorScheme.themeWhite,
                                      ),
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            position: ElTooltipPosition.bottomStart,
                            color: context.colorScheme.mainBlue,
                            radius: Radius.circular(16.r),
                            showChildAboveOverlay: true,
                            showModal: true,
                            child: SvgPicture.asset(
                              Assets.icons.icCommonInfoTooltip,
                              height: cubit.appCubit.isTablet ? 18 : 18.w,
                              width: cubit.appCubit.isTablet ? 18 : 18.w,
                              colorFilter: ColorFilter.mode(
                                context.colorScheme.mainPrimary,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ],
                      ),
                      GestureDetector(
                        onTap: () {
                          if (state.selectedTabs.length ==
                              state.allTabs.length) {
                            cubit.deselectAll();
                          } else {
                            cubit.selectAll();
                          }
                        },
                        child: CommonText(
                          state.selectedTabs.length == state.allTabs.length
                              ? S.current.deselect
                              : S.current.select_all,
                          style: TextStyle(
                            color: context.colorScheme.mainBlue,
                            fontSize: context.isTablet ? 16 : 14.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      )
                    ],
                  ),
                  AppConstants.kSpacingItem24,
                  Wrap(
                    spacing: 12,
                    runSpacing: 20,
                    children: state.allTabs.map((tab) {
                      bool isSelected = state.selectedTabs.contains(tab);
                      return GestureDetector(
                        onTap: () => cubit.toggleTab(tab),
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 10.h,
                              ),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? context.colorScheme.mainBlue
                                        .withOpacity(0.24)
                                    : context.colorScheme.mainNeutral,
                                borderRadius: BorderRadius.circular(100.r),
                              ),
                              child: CommonText(
                                tab.displayName,
                                style: TextStyle(
                                  color: isSelected
                                      ? context.colorScheme.mainPrimary
                                      : context.colorScheme.mainGray,
                                  fontSize: context.isTablet ? 16 : 14.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            if (isSelected)
                              Positioned(
                                right: 0,
                                top: -8,
                                child: CircleAvatar(
                                  backgroundColor: context.colorScheme.mainBlue,
                                  radius: 10.r,
                                  child: CommonText(
                                    '${cubit.getTabIndex(tab)}',
                                    style: TextStyle(
                                      color: context.colorScheme.themeWhite,
                                      fontSize: context.isTablet ? 14 : 12.sp,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    }).toList(),
                  )
                ],
              ),
            ),
          ),
          bottomNavigationBar: Container(
            margin: EdgeInsets.symmetric(vertical: 48.h, horizontal: 16.w),
            child: CommonButton(
              minWidth: double.infinity,
              height: context.isTablet ? 60 : 44.h,
              radiusSize: 24.r,
              onPress: () async {
                // Track selected tabs before saving
                _trackSavedTabItems(state.selectedTabs, "CustomTabSetting");
                AnalyticsService.logAnalyticsEventNoParam(
                    eventName: "CustomTabSetting_Save_Click");
                await cubit.saveSelectedTabs();
                if (mounted) {
                  // ignore: use_build_context_synchronously
                  Navigator.pop(context);
                }
              },
              backgroundColor: state.isSaveButtonEnabled
                  ? context.colorScheme.mainBlue
                  : context.colorScheme.mainNeutral,
              isEnable: state.isSaveButtonEnabled,
              btnText: S.current.save,
              btnTextStyle: TextStyle(
                color: context.colorScheme.themeWhite,
                fontSize: context.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      },
    );
  }
}
