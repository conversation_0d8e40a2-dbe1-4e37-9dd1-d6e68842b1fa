import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'package:ffmpeg_kit_flutter_full_gpl/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_full_gpl/ffprobe_kit.dart';
import 'package:ffmpeg_kit_flutter_full_gpl/return_code.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/shorts_detail/short_details_state.dart';
import 'package:path_provider/path_provider.dart';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;
import 'package:video_player/video_player.dart';

class ShortsDetailCubit extends BaseCubit<ShortDetailsState> {
  ShortsDetailCubit(super.initialState);

  final _taskResultApiService = GetIt.instance.get<ResultTaskApiServiceImpl>();
  final _creditApiServiceImpl = GetIt.instance.get<CreditApiServiceImpl>();
  final _localService = GetIt.instance.get<LocalService>();

  final _createShortApiService =
      GetIt.instance.get<CreateShortsApiServiceImpl>();
  final _cacheService = GetIt.instance.get<CacheService>();
  late final bool _isCaptionEnabled;
  late final bool _isCreateAudioShorts;
  VideoPlayerController? _videoController;
  Timer? _progressMessageTimer;
  int _currentMessageIndex = 0;

  VideoPlayerController? get videoController => _videoController;
  final ValueNotifier<String> titleNotifier = ValueNotifier<String>('');

  void initData(
      bool isCaptionEnabled, bool isCreateAudioShorts, bool isQuizVideo) {
    _isCaptionEnabled = isCaptionEnabled;
    _isCreateAudioShorts = isCreateAudioShorts;
  }

  // Main method that handles URL-based initialization
  Future<void> initVideoPlayer(String videoUrl) async {
    try {
      final videoFilePath = await _cacheService.getCachedFilePath(videoUrl);
      await initializeVideoPlayerWithFile(videoFilePath);

      // Update state with URL-specific data
      emit(state.copyWith(
          selectedUrlShortsBackgrounds: videoUrl,
          currentPlayingFilePath: videoFilePath));
    } catch (e, stackTrace) {
      handleVideoInitializationError(e, stackTrace);
    }
  }

  // Reusable method for file-based initialization
  Future<void> initializeVideoPlayerWithFile(String videoFilePath) async {
    // Dispose existing controller
    await _videoController?.dispose();
    _videoController = null;

    // Create and initialize new controller
    _videoController = VideoPlayerController.file(File(videoFilePath));

    // Initialize with error handling
    await _videoController!.initialize().timeout(
      const Duration(seconds: 10),
      onTimeout: () {
        AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.shorts_detail_video_init_fail);
      },
    );

    // Configure player settings
    _videoController!.setLooping(true);
    _videoController!.play();
    emit(state.copyWith(currentPlayingFilePath: videoFilePath));
  }

  // Error handling method
  void handleVideoInitializationError(
      Object error, StackTrace stackTrace) async {
    AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.shorts_detail_merge_video_exception);
    await _videoController?.dispose();
    _videoController = null;

    emit(state.copyWith(
      processingStatus: ProcessingStatus.error,
      oneShotEvent: ShortsDetailOneShotEvent.showError,
    ));
  }

  Future<void> generateShorts(String noteId, int durationOption, String voiceId,
      String selectedShortUrl, String videoId) async {
    runCubitCatching(
      handleLoading: false,
      action: () async {
        emit(state.copyWith(processingStatus: ProcessingStatus.apiGenerating));

        final taskDto = await _createShortApiService.createShorts(
            durationOption, voiceId, noteId, false, videoId);
        final shortsDto = await _taskResultApiService.getTaskResultShortsVideo(
            taskId: taskDto.taskId);
        _refreshUserCredits();
        emit(state.copyWith(processingStatus: ProcessingStatus.downloading));
        final videoFilePath =
            await _cacheService.getCachedFilePath(selectedShortUrl);
        final audioFilePath =
            await _cacheService.getCachedFilePath(shortsDto.audioUrl);

        final directory = await getApplicationDocumentsDirectory();

        // Save subtitle to file
        final subtitlePath = path.join(
          directory.path,
          'subtitle_${DateTime.now().millisecondsSinceEpoch}.ass',
        );
        await File(subtitlePath)
            .writeAsString(decodeBase64ToString(shortsDto.subtitleRawData));
        // Generate output path
        final outputPath = path.join(
          directory.path,
          'output_${DateTime.now().millisecondsSinceEpoch}.mp4',
        );
        emit(state.copyWith(
            subtitleFilePath: subtitlePath, audioFilePath: audioFilePath));
        await _processVideo(videoFilePath, _isCaptionEnabled, audioFilePath,
            subtitlePath, outputPath);
      },
      doOnError: (exception) async {
        emit(state.copyWith(processingStatus: ProcessingStatus.error));
      },
    );
  }

  Future<void> generateAudio(
      String noteId, int durationOption, String voiceId) async {
    runCubitCatching(
        action: () async {
          emit(
              state.copyWith(processingStatus: ProcessingStatus.apiGenerating));
          final taskDto = await _createShortApiService.createShorts(
              durationOption, voiceId, noteId, true, '');
          final shortsDto = await _taskResultApiService
              .getTaskResultShortsVideo(taskId: taskDto.taskId);
          _refreshUserCredits();
          emit(state.copyWith(processingStatus: ProcessingStatus.processing));
          final audioFilePath =
              await _cacheService.getCachedFilePath(shortsDto.audioUrl);
          emit(state.copyWith(
              processingStatus: ProcessingStatus.complete,
              audioFilePath: audioFilePath));
        },
        handleLoading: false);
  }

  Future<void> generateShortsQuiz(
    int duration,
    String voiceId,
    String noteId,
    String templateId,
  ) async {
    runCubitCatching(
      handleLoading: false,
      action: () async {
        emit(state.copyWith(
            shortQuizOneShotEvent: ShortQuizOneShotEvent.loading));

        // Start progress message rotation
        _startProgressMessageRotation();

        final taskDto = await _createShortApiService.createShortsQuiz(
            duration, voiceId, noteId, templateId);

        /// Get Result Short
        final shortQuizDto =
            await _taskResultApiService.getTaskResultShortQuizVideo(
          taskId: taskDto.taskId,
        );
        _refreshUserCredits();

        // Stop progress message rotation
        _stopProgressMessageRotation();

        // Update state with the quiz video URL and success state
        emit(state.copyWith(
          quizVideoUrl: shortQuizDto.quizVideoUrl,
          shortQuizOneShotEvent: ShortQuizOneShotEvent.success,
        ));
      },
      doOnError: (exception) async {
        _stopProgressMessageRotation();
        emit(state.copyWith(
          shortQuizOneShotEvent: ShortQuizOneShotEvent.error,
        ));
      },
    );
  }

  String _buildFFmpegCommand({
    required String videoFilePath,
    required String audioFilePath,
    required String subtitleFilePath,
    required String outputPath,
    required bool isCaptionEnabled,
    required double audioDuration,
  }) {
    final commands = [
      '-y',
      '-stream_loop -1 -i "$videoFilePath"',
      '-i "$audioFilePath"',
    ];

    if (isCaptionEnabled) {
      final fontName = Platform.isIOS
          ? 'Arial,Helvetica,San Francisco,Times New Roman,Geneva,Verdana'
          : 'Roboto,Droid Sans,Noto Sans,Arial,Sans-serif';
      commands.add('-vf "scale=trunc(iw/2)*2:trunc(ih/2)*2,'
          'subtitles=$subtitleFilePath'
          ':force_style=\'Fontname=$fontName,'
          'FontSize=20,'
          'Outline=2,'
          'Spacing=0.2\''
          ':original_size=2160x3840'
          ':fontsdir=${Platform.isIOS ? '/System/Library/Fonts' : '/system/fonts'}'
          '"');
    }

    commands.addAll([
      '-map 0:v',
      '-map 1:a',
      '-c:v libx264',
      '-preset ultrafast',
      '-profile:v high',
      '-level:v 5.2',
      '-b:v 6M',
      '-maxrate 45M',
      '-bufsize 90M',
      '-c:a aac',
      '-b:a 384k',
      '-ar 48000',
      '-t $audioDuration',
      '-movflags +faststart',
      '"$outputPath"'
    ]);
    if (Platform.isAndroid) {
      commands.add('-pix_fmt yuv420p ');
    }
    return commands.join(' ');
  }

  Future<double> _getAudioDuration(String audioFilePath) async {
    final session = await FFprobeKit.execute(
        '-i "$audioFilePath" -show_entries format=duration -v quiet -of csv="p=0"');
    final durationStr = (await session.getOutput())?.trim();
    final duration = double.tryParse(durationStr ?? '0') ?? 0;

    if (duration <= 0) {
      AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.shorts_detail_audio_empty);
    }
    return duration;
  }

  Future<void> _processVideo(
      String videoFilePath,
      bool isCaptionEnabled,
      String audioFilePath,
      String subtileFilePath,
      String videoOutputPath) async {
    try {
      emit(state.copyWith(
          processingStatus: ProcessingStatus.processing,
          audioFilePath: audioFilePath,
          subtitleFilePath: subtileFilePath));
      debugPrint('Processing video with audio: $audioFilePath');

      // Get audio duration first
      final audioDuration = await _getAudioDuration(audioFilePath);

      // Prepare FFmpeg command
      final command = _buildFFmpegCommand(
        videoFilePath: videoFilePath,
        audioFilePath: audioFilePath,
        subtitleFilePath: subtileFilePath,
        outputPath: videoOutputPath,
        isCaptionEnabled: isCaptionEnabled,
        audioDuration: audioDuration,
      );

      debugPrint('FFmpeg command: $command');

      // Execute FFmpeg command
      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        // Verify the output file exists and has size > 0
        final map = HashMap();
        map.addAll(state.mergedVideosMap);
        map.addAll({state.selectedUrlShortsBackgrounds: videoOutputPath});

        // Initialize video player with the new merged video
        await initializeVideoPlayerWithFile(videoOutputPath);

        emit(state.copyWith(
          mergedVideosMap: Map.from(map),
          currentPlayingFilePath: videoOutputPath,
          processingStatus: ProcessingStatus.complete,
          oneShotEvent: ShortsDetailOneShotEvent.videoProcessingComplete,
        ));
      } else {
        AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.shorts_detail_return_code_fail);
      }
    } catch (e, stackTrace) {
      handleVideoInitializationError(e, stackTrace);
    }
  }

  void toggleWatermark() {
    emit(state.copyWith(isWatermarkEnable: !state.isWatermarkEnable));
  }

  /// Handles quiz video selection change
  void handleQuizVideoChange(
    String? newValue,
    List<BackgroundQuizVideoDto> quizBackgroundList,
    int durationOption,
    String voiceId,
    String noteId,
  ) {
    if (newValue != null && newValue.isNotEmpty) {
      final selectedItem =
          quizBackgroundList.firstWhere((i) => i.videoUrl == newValue);

      // Update title and selected video URL in state
      titleNotifier.value = selectedItem.title;
      emit(state.copyWith(
        selectedQuizVideoUrl: newValue,
        quizVideoUrl: newValue, // Also update quizVideoUrl to keep it in sync
      ));

      // Regenerate quiz short with new templateId
      generateShortsQuiz(
        durationOption,
        voiceId,
        noteId,
        selectedItem.templateId,
      );
    }
  }

  /// Updates the selected quiz video URL in the state
  void updateSelectedQuizVideoUrl(String url) {
    emit(state.copyWith(
      selectedQuizVideoUrl: url,
      quizVideoUrl: url, // Also update quizVideoUrl to keep it in sync
    ));
  }

  /// Handles background selection change from dropdown
  void handleBackgroundChange(
      String? newValue, List<BackgroundVideoDto> backgroundList) {
    if (newValue != null) {
      final selectedItem =
          backgroundList.firstWhere((i) => i.videoUrl == newValue);
      titleNotifier.value = selectedItem.title;
      selectBackground(newValue);
    }
  }

  /// Updates the selected background and initializes the video player
  Future<void> selectBackground(String url) async {
    emit(state.copyWith(selectedUrlShortsBackgrounds: url));

    if (state.mergedVideosMap.containsKey(url)) {
      await initializeVideoPlayerWithFile(state.mergedVideosMap[url] ?? '');
    } else {
      await initVideoPlayer(url);
      final directory = await getApplicationDocumentsDirectory();
      final outputPath = path.join(
        directory.path,
        'output_${DateTime.now().millisecondsSinceEpoch}.mp4',
      );

      _processVideo(state.currentPlayingFilePath, _isCaptionEnabled,
          state.audioFilePath, state.subtitleFilePath, outputPath);
    }
  }

  Future<void> downloadVideo(
    BuildContext context, {
    required GlobalKey buttonKey,
    required bool isDownload,
    required bool isShort,
  }) async {
    isShort
        ? AnalyticsService.logAnalyticsEventNoParam(
            eventName: isDownload
                ? EventName.shorts_detail_download_clicked
                : EventName.shorts_detail_share_clicked)
        : AnalyticsService.logAnalyticsEventNoParam(
            eventName: isDownload
                ? EventName.podcast_detail_download_clicked
                : EventName.podcast_detail_share_clicked);
    try {
      CommonDialogs.showLoadingDialog();
      // _isCreateAudioShorts is true for PodcastDetailPage, false for ShortsDetailPage
      final filePath = _isCreateAudioShorts
          ? state.audioFilePath
          : state.currentPlayingFilePath;

      // Get position for share sheet
      Rect? sharePositionOrigin;
      try {
        final RenderBox? box =
            buttonKey.currentContext?.findRenderObject() as RenderBox?;
        if (box != null && box.hasSize) {
          final Offset position = box.localToGlobal(Offset.zero);
          sharePositionOrigin = Rect.fromLTWH(
            position.dx,
            position.dy,
            box.size.width,
            box.size.height,
          );
        } else {
          // Fallback to screen center if button position not found
          final size = MediaQuery.of(context).size;
          sharePositionOrigin = Rect.fromCenter(
              center: Offset(size.width / 2, size.height / 2),
              width: 100,
              height: 100);
        }
      } catch (e) {
        final size = MediaQuery.of(context).size;
        sharePositionOrigin = Rect.fromCenter(
            center: Offset(size.width / 2, size.height / 2),
            width: 100,
            height: 100);
      }

      // Close loading before share
      CommonDialogs.closeLoading();

      await ShareService().shareFile(
        filePath: filePath,
        sharePositionOrigin: sharePositionOrigin,
        eventName: isShort
            ? isDownload
                ? EventName.shorts_detail_export_success
                : EventName.shorts_detail_share_success
            : isDownload
                ? EventName.podcast_detail_download_success
                : EventName.podcast_detail_share_success,
      );

      emit(state.copyWith(
        didShareOrDownloadShortsResult: true,
        oneShotEvent: ShortsDetailOneShotEvent.videoSaved,
      ));
    } catch (e) {
      AnalyticsService.logAnalyticsEventNoParam(
          eventName: isDownload
              ? EventName.shorts_detail_download_exception
              : EventName.shorts_detail_share_exception);
      CommonDialogs.closeLoading();
    }
  }

  void _refreshUserCredits() async {
    final creditInfo = await _creditApiServiceImpl.fetchDataCredit();
    final user = appCubit.getAppUser().copyWith(
        youtubeCredit: creditInfo.youtubeCredit,
        webCredit: creditInfo.webCredit,
        audioCredit: creditInfo.audioCredit,
        rewardCredits: creditInfo.rewardCredits,
        shortsCredit: creditInfo.shortsCredit,
        documentCredit: creditInfo.documentCredit,
        userType: mapUserTypeStringToEnum(creditInfo.userType));
    appCubit.updateAppUser(user);
    _localService.saveAppUser(user);
  }

  void resetEnumState() {
    if (!isClosed) {
      emit(state.copyWith(oneShotEvent: ShortsDetailOneShotEvent.none));
    }
  }

  // Special method for initializing video controller directly from URL
  Future<bool> initVideoPlayerDirectFromUrl(String videoUrl) async {
    try {
      // Get cached file path from URL
      final videoFilePath = await _cacheService.getCachedFilePath(videoUrl);

      // Initialize video player with the cached file
      await initializeVideoPlayerWithFile(videoFilePath);

      // Update state with success
      emit(state.copyWith(
        shortQuizOneShotEvent: ShortQuizOneShotEvent.success,
        currentPlayingFilePath: videoFilePath,
      ));

      return true;
    } catch (e, _) {
      emit(state.copyWith(
        shortQuizOneShotEvent: ShortQuizOneShotEvent.error,
      ));
      return false;
    }
  }

  final List<String> _quizProgressMessages = [
    S.current.creating_quiz,
    S.current.gen_quiz_bgr,
    S.current.gen_ai_voice,
    S.current.map_all_together,
    S.current.making_amazing,
  ];

  void _startProgressMessageRotation() {
    // Cancel any existing timer
    _progressMessageTimer?.cancel();

    // Update immediately with first message
    _currentMessageIndex = 0;
    emit(state.copyWith(
        progressMessage: _quizProgressMessages[_currentMessageIndex]));

    // Start the rotation timer (10 seconds)
    _progressMessageTimer =
        Timer.periodic(const Duration(seconds: 10), (timer) {
      // Increment the message index
      _currentMessageIndex++;

      // If we've reached the last message, cancel the timer and stay on this message
      if (_currentMessageIndex >= _quizProgressMessages.length - 1) {
        _currentMessageIndex =
            _quizProgressMessages.length - 1; // Set to last message
        timer.cancel(); // Stop the timer
      }

      // Update state with the current message
      emit(state.copyWith(
          progressMessage: _quizProgressMessages[_currentMessageIndex]));
    });
  }

  void _stopProgressMessageRotation() {
    _progressMessageTimer?.cancel();
    _progressMessageTimer = null;
  }

  @override
  Future<void> close() {
    _progressMessageTimer?.cancel();
    return super.close();
  }
}
