import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class CommonDialogs {
  static bool _isDialogLoadingOpened = false;

  static BuildContext? get _context =>
      GetIt.instance.get<NavigationService>().context;

  static bool get isLoadingDialogOpen => _isDialogLoadingOpened;

  static void showToast(
    String message, {
    ToastGravity gravity = ToastGravity.CENTER,
    Toast? length,
    Color? textColor,
    int? timeInSecForIosWeb,
  }) {
    Fluttertoast.showToast(
        msg: message,
        toastLength: length ?? Toast.LENGTH_SHORT,
        gravity: gravity,
        timeInSecForIosWeb: timeInSecForIosWeb ?? 1,
        backgroundColor: _context!.colorScheme.mainPrimary,
        textColor: textColor ?? _context!.colorScheme.mainSecondary,
        fontSize: 14.0);
  }

  static void showLoadingDialog({
    bool disableWhenClickOutside = false,
    ValueNotifier<String>? dialogText,
  }) async {
    closeLoading();
    _isDialogLoadingOpened = true;
    showDialog(
      barrierDismissible: disableWhenClickOutside,
      context: _context!,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: ValueListenableBuilder(
            valueListenable: dialogText ?? ValueNotifier(S.current.loading),
            builder: (BuildContext context, value, Widget? child) {
              return AlertDialog(
                insetPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                backgroundColor: Colors.transparent,
                elevation: 0,
                clipBehavior: Clip.antiAliasWithSaveLayer,
                content: Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Center(
                          child: CupertinoActivityIndicator(
                        radius: 16.r,
                        color: context.colorScheme.themeWhite,
                      )),
                      AppConstants.kSpacingItem2,
                      CommonText(
                        value,
                        style: TextStyle(
                          fontSize: context.isTablet ? 16 : 14.sp,
                        ),
                        appFontWeight: AppFontWeight.regular,
                        textColor: context.colorScheme.mainPrimary,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  static void closeLoading() {
    if (CommonDialogs._isDialogLoadingOpened) {
      CommonDialogs._isDialogLoadingOpened = false;
      Navigator.of(_context!).pop();
    }
  }

  static Future<dynamic> buildDeleteDialog(
    BuildContext context, {
    required String title,
    required String content,
    Color? colorButtonDelete,
    Color? colorButtonCancel,
    required Function([bool?])?
        onPressedDeleteButton, // Modified to accept optional bool
    required Function()? onPressedCancelButton,
    String? headerImageAssetFile,
    bool isFolderDeleteDialog = false,
    int folderNoteCount = 0, // For the "Delete %s notes in folder" text
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        bool isCheckboxChecked = false; // Local state for checkbox

        return StatefulBuilder(
          builder: (context, setState) {
            final dialogWidthTablet = MediaQuery.of(context).size.width * 0.5;
            final dialogWidth = MediaQuery.of(context).size.width;

            return Dialog(
              insetPadding: EdgeInsets.symmetric(
                horizontal: context.isTablet ? 24.0 : 24.0.w,
              ),
              backgroundColor: context.colorScheme.mainSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Container(
                width: context.isTablet ? dialogWidthTablet : dialogWidth,
                constraints: BoxConstraints(
                  maxWidth: context.isTablet ? dialogWidthTablet : dialogWidth,
                ),
                child: Padding(
                  padding: EdgeInsets.all(24.w),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (headerImageAssetFile != null) ...[
                        SvgPicture.asset(
                          headerImageAssetFile,
                          width: context.isTablet ? 140 : 140.w,
                          height: context.isTablet ? 140 : 140.h,
                        ),
                      ],
                      AppConstants.kSpacingItem16,
                      CommonText(
                        title,
                        style: TextStyle(
                          color: context.colorScheme.mainPrimary,
                          fontSize: context.isTablet ? 22 : 20.sp,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      AppConstants.kSpacingItem8,
                      CommonText(
                        content,
                        style: TextStyle(
                          fontSize: context.isTablet ? 16 : 14.sp,
                          fontWeight: FontWeight.w400,
                          color:
                              context.colorScheme.mainPrimary.withOpacity(0.8),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      // Add checkbox row for folder delete dialog
                      if (isFolderDeleteDialog && folderNoteCount > 0 ) ...[
                        AppConstants.kSpacingItem16,
                        Row(
                          children: [
                            Checkbox(
                              value: isCheckboxChecked,
                              onChanged: (bool? value) {
                                setState(() {
                                  isCheckboxChecked = value ?? false;
                                });
                              },
                              activeColor: context.colorScheme.mainBlue,
                            ),
                            Expanded(
                              child: CommonText(
                                "Delete $folderNoteCount notes in folder",
                                style: TextStyle(
                                  fontSize: context.isTablet ? 16 : 14.sp,
                                  fontWeight: FontWeight.w400,
                                  color: context.colorScheme.mainPrimary
                                      .withOpacity(0.8),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                      AppConstants.kSpacingItem16,
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                                onPressedCancelButton?.call();
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24.r),
                                  color: context.colorScheme.mainNeutral,
                                ),
                                height: context.isTablet ? 48 : 48.h,
                                alignment: Alignment.center,
                                child: CommonText(
                                  S.current.cancel,
                                  style: TextStyle(
                                    color: context.colorScheme.mainPrimary,
                                    fontWeight: FontWeight.w500,
                                    fontSize: context.isTablet ? 18 : 16.sp,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          AppConstants.kSpacingItemW8,
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.of(context).pop();
                                // Pass checkbox state if it's folder delete dialog, otherwise call without parameter
                                if (isFolderDeleteDialog) {
                                  onPressedDeleteButton
                                      ?.call(isCheckboxChecked);
                                } else {
                                  onPressedDeleteButton?.call();
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24.r),
                                  color: context.colorScheme.mainBlue,
                                ),
                                height: context.isTablet ? 48 : 48.h,
                                alignment: Alignment.center,
                                child: CommonText(
                                  S.current.delete,
                                  style: TextStyle(
                                    color: AppColors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: context.isTablet ? 18 : 16.sp,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  static Future<dynamic> showInfoDialog(
    BuildContext context, {
    String? mainButtonTitle,
    String? subButtonTitle,
    required String title,
    required String content,
    Func0? onPressMainButton,
    Func0? onPressSubButton,
  }) {
    return showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Theme(
        data: ThemeData.dark(),
        child: CupertinoAlertDialog(
          title: Text(
            title,
            style: TextStyle(
              color: Colors.white,
              fontSize: context.isTablet ? 20 : 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            content,
            style: TextStyle(
              fontSize: context.isTablet ? 16 : 13.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              onPressed: () {
                Navigator.pop(context);
                onPressMainButton?.call();
              },
              child: Text(
                mainButtonTitle ?? S.current.got_it,
                style: TextStyle(
                  fontSize: context.isTablet ? 22 : 14.sp,
                  fontWeight: FontWeight.w600,
                  color: context.colorScheme.mainBlue,
                ),
              ),
            ),
            if (subButtonTitle != null)
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.pop(context);
                  onPressSubButton?.call();
                },
                child: Text(
                  subButtonTitle,
                  style: TextStyle(
                    fontSize: context.isTablet ? 22 : 14.sp,
                    fontWeight: FontWeight.w600,
                    color: context.colorScheme.mainGray,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  static Future<dynamic> showCongratulationDialog(
      BuildContext context,
      String title,
      String content,
      String textButton,
      VoidCallback actionButton) {
    return showCupertinoDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Theme(
              data: ThemeData.dark(),
              child: CupertinoAlertDialog(
                title: Text(
                  title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: context.isTablet ? 22 : 18.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                content: Text(
                  content,
                  style: TextStyle(
                    fontSize: context.isTablet ? 15 : 10.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.white,
                  ),
                ),
                actions: [
                  CupertinoDialogAction(
                    onPressed: () {
                      Navigator.pop(context);
                      actionButton();
                    },
                    child: Text(
                      textButton,
                      style: TextStyle(
                        fontSize: context.isTablet ? 17 : 14.sp,
                        fontWeight: FontWeight.w600,
                        color: context.colorScheme.mainBlue,
                      ),
                    ),
                  ),
                ],
              ),
            ));
  }

  static Future<dynamic> showQuizCongratulationDialog(
    BuildContext context,
    String title,
    VoidCallback onReviewQuiz, {
    required int totalQuestions,
    required int correctAnswers,
    required int wrongAnswers,
  }) {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.quizzes_scr_show_dialog,
    );
    return showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (context) {
        final dialogWidthTablet = MediaQuery.of(context).size.width * 0.5;
        final dialogWidth = MediaQuery.of(context).size.width;
        return Dialog(
          child: Container(
            width: context.isTablet ? dialogWidthTablet : dialogWidth,
            constraints: BoxConstraints(
              maxWidth: context.isTablet ? dialogWidthTablet : dialogWidth,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.r),
              color: context.colorScheme.mainNeutral,
            ),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 24.h),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.icons.icMcConf,
                        width: context.isTablet ? 140 : 140.w,
                        height: context.isTablet ? 140 : 140.h,
                      ),
                      AppConstants.kSpacingItem8,
                      CommonText(
                        title,
                        style: TextStyle(
                          fontSize: context.isTablet ? 22 : 20.sp,
                          fontWeight: FontWeight.w600,
                          color: context.colorScheme.mainPrimary,
                        ),
                      ),
                      AppConstants.kSpacingItem16,
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildStatColumn(
                              context,
                              icon: Assets.icons.icBlueTarget,
                              value: totalQuestions.toString(),
                              label: S.current.total,
                            ),
                            _buildStatColumn(
                              context,
                              icon: Assets.icons.icCorrect,
                              value: correctAnswers.toString(),
                              label: S.current.correct,
                            ),
                            _buildStatColumn(
                              context,
                              icon: Assets.icons.icWrong,
                              value: wrongAnswers.toString(),
                              label: S.current.wrong,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: context.isTablet ? 16 : 11.0,
                  right: context.isTablet ? 16 : 11.0,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      onReviewQuiz();
                    },
                    child: SvgPicture.asset(
                      Assets.icons.icClose,
                      width: context.isTablet ? 24 : 24.w,
                      height: context.isTablet ? 24 : 24.w,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static Widget _buildStatColumn(
    BuildContext context, {
    required String icon,
    required String value,
    required String label,
  }) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                icon,
                width: _context!.isTablet ? 24 : 24.w,
                height: _context!.isTablet ? 24 : 24.h,
              ),
              AppConstants.kSpacingItemW4,
              Text(
                value,
                style: TextStyle(
                  fontSize: _context!.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w500,
                  color: context.colorScheme.mainPrimary,
                ),
              ),
            ],
          ),
          AppConstants.kSpacingItem2,
          CommonText(
            label,
            style: TextStyle(
              fontSize: _context!.isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w500,
              color: context.colorScheme.mainGray,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  static Future<dynamic> showDialogFreeTrial(
    BuildContext context, {
    required String title,
    required String content,
    required String button1Text,
    required String button2Text,
    required VoidCallback button1Action,
    required VoidCallback button2Action,
  }) {
    return showCupertinoDialog(
      context: context,
      builder: (context) => Theme(
        data: ThemeData.dark(),
        child: CupertinoAlertDialog(
          title: Text(
            title,
            style: TextStyle(
              color: Colors.white,
              fontSize: context.isTablet ? 20 : 17.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            content,
            style: TextStyle(
              fontSize: context.isTablet ? 17 : 13.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray.withOpacity(0.6),
            ),
          ),
          actions: [
            CupertinoDialogAction(
              onPressed: () {
                Navigator.pop(context);
                button1Action();
              },
              child: Text(
                button1Text,
                style: TextStyle(
                  fontSize: context.isTablet ? 17 : 15.sp,
                  fontWeight: FontWeight.w400,
                  color: context.colorScheme.themeWhite,
                ),
              ),
            ),
            CupertinoDialogAction(
              onPressed: () {
                Navigator.pop(context);
                button2Action();
              },
              child: Text(
                button2Text,
                style: TextStyle(
                  fontSize: context.isTablet ? 20 : 17.sp,
                  fontWeight: FontWeight.w600,
                  color: context.colorScheme.mainBlue,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void showErrorDialog(
    BuildContext context, {
    String? title,
    String contentButton = 'OK',
    String? content,
    Func0? onPressed,
  }) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) => Theme(
        data: ThemeData.dark(),
        child: CupertinoAlertDialog(
          title: title != null
              ? Text(
                  title,
                  style: TextStyle(
                    color: context.colorScheme.mainPrimary,
                    fontSize: context.isTablet ? 18 : 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                )
              : null,
          content: content != null
              ? Text(
                  content,
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: context.isTablet ? 16 : 13.sp,
                    fontWeight: FontWeight.w400,
                  ),
                )
              : null,
          actions: [
            CupertinoDialogAction(
              onPressed: () {
                Navigator.pop(context);
                onPressed?.call();
              },
              child: Text(
                contentButton,
                style: TextStyle(
                  color: context.colorScheme.mainBlue,
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

typedef CallBack = dynamic Function();
