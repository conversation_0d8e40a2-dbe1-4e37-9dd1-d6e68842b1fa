// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(date) => "Your trial will expire in ${date} days.";

  static String m1(images) => "${images} photos have been uploaded";

  static String m2(price, date) => "Your next bill is for ${price} on ${date}.";

  static String m3(uid) => "uid ${uid} copied to clipboard!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "Regenerate": MessageLookupByLibrary.simpleMessage("Regenerate"),
    "a_to_z": MessageLookupByLibrary.simpleMessage("A to Z"),
    "about_us": MessageLookupByLibrary.simpleMessage("About Us"),
    "access_notex_web": MessageLookupByLibrary.simpleMessage(
      "Access NoteX web",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Account"),
    "account_basic": MessageLookupByLibrary.simpleMessage("Basic"),
    "account_content_basic": MessageLookupByLibrary.simpleMessage(
      "Limit AI experience",
    ),
    "account_content_pro": MessageLookupByLibrary.simpleMessage(
      "Unlock unlimited AI experience",
    ),
    "account_lifetime": MessageLookupByLibrary.simpleMessage("Lifetime"),
    "achieve_more": MessageLookupByLibrary.simpleMessage("ACHIEVE MORE"),
    "action_items": MessageLookupByLibrary.simpleMessage("Action Items"),
    "actionable_intelligence": MessageLookupByLibrary.simpleMessage(
      "actionable intelligence",
    ),
    "active_description": MessageLookupByLibrary.simpleMessage(
      "An active description could not be found.",
    ),
    "active_recall": MessageLookupByLibrary.simpleMessage("active recall"),
    "add_focus": MessageLookupByLibrary.simpleMessage(
      "Add specific focus areas or requirements...",
    ),
    "add_folder": MessageLookupByLibrary.simpleMessage("Move to Folder"),
    "add_note": MessageLookupByLibrary.simpleMessage("Add Note"),
    "add_password": MessageLookupByLibrary.simpleMessage("Add password"),
    "add_password_to_public": MessageLookupByLibrary.simpleMessage(
      "Add a password to the public link",
    ),
    "add_to": MessageLookupByLibrary.simpleMessage("Move to"),
    "add_to_notes": MessageLookupByLibrary.simpleMessage("Add to Note"),
    "additional_ins": MessageLookupByLibrary.simpleMessage(
      "Additional Instructions (opt.)",
    ),
    "advance_mode": MessageLookupByLibrary.simpleMessage("Advanced Mode"),
    "advanced": MessageLookupByLibrary.simpleMessage("Advanced"),
    "afternoon_content": MessageLookupByLibrary.simpleMessage(
      "Small notes, big impact",
    ),
    "afternoon_content_1": MessageLookupByLibrary.simpleMessage(
      "Thoughts captured, mind free",
    ),
    "afternoon_content_3": MessageLookupByLibrary.simpleMessage(
      "Order amid the chaos",
    ),
    "afternoon_content_4": MessageLookupByLibrary.simpleMessage(
      "Your ideas, organized",
    ),
    "afternoon_content_5": MessageLookupByLibrary.simpleMessage(
      "Clarity in progress",
    ),
    "afternoon_content_6": MessageLookupByLibrary.simpleMessage(
      "Keep what matters",
    ),
    "ai_audio_transcription_per_day": MessageLookupByLibrary.simpleMessage(
      "3 AI Audio Transcription per day *",
    ),
    "ai_chat": MessageLookupByLibrary.simpleMessage("Nova AI"),
    "ai_chat_assistant": MessageLookupByLibrary.simpleMessage(
      "AI Chat Assistant",
    ),
    "ai_chat_with_notes": MessageLookupByLibrary.simpleMessage(
      "AI Chat With Notes",
    ),
    "ai_insight": MessageLookupByLibrary.simpleMessage("AI Insight"),
    "ai_learning": MessageLookupByLibrary.simpleMessage("AI Learning"),
    "ai_learning_companion": MessageLookupByLibrary.simpleMessage(
      "I\'m Nova AI from NoteX.",
    ),
    "ai_note_create": MessageLookupByLibrary.simpleMessage("AI Notes Creation"),
    "ai_note_creation": MessageLookupByLibrary.simpleMessage(
      "AI Notes Creation",
    ),
    "ai_note_from": MessageLookupByLibrary.simpleMessage("AI Notes from Audio"),
    "ai_notes_10": MessageLookupByLibrary.simpleMessage(
      "Unlimited AI notes from YouTube & Documents",
    ),
    "ai_notes_3": MessageLookupByLibrary.simpleMessage(
      "3 AI notes per day from Recording & Audio upload (up to 60min per file)",
    ),
    "ai_notes_from": MessageLookupByLibrary.simpleMessage(
      "AI Notes from \nYouTube, Web, Docs",
    ),
    "ai_short_1": MessageLookupByLibrary.simpleMessage(
      "3 AI Short Videos generation per day",
    ),
    "ai_short_3": MessageLookupByLibrary.simpleMessage(
      "5 AI Short Videos generation per day (beta)",
    ),
    "ai_short_video": MessageLookupByLibrary.simpleMessage("AI Short Videos"),
    "ai_study_practice": MessageLookupByLibrary.simpleMessage(
      "AI Study Practice",
    ),
    "ai_study_tools": MessageLookupByLibrary.simpleMessage("AI Study Tools"),
    "ai_summarize": MessageLookupByLibrary.simpleMessage("AI Summarize"),
    "ai_transcription": MessageLookupByLibrary.simpleMessage(
      "AI Transcription",
    ),
    "ai_workflow": MessageLookupByLibrary.simpleMessage("AI Workflow"),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "all_note": MessageLookupByLibrary.simpleMessage("All Notes"),
    "all_note_in_folder": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to remove this folder?",
    ),
    "all_tabs": MessageLookupByLibrary.simpleMessage("All Tabs"),
    "allow": MessageLookupByLibrary.simpleMessage("Allow"),
    "almost_done": MessageLookupByLibrary.simpleMessage("Almost done"),
    "and": MessageLookupByLibrary.simpleMessage("and"),
    "answer": MessageLookupByLibrary.simpleMessage("Answer"),
    "anyone_with_link": MessageLookupByLibrary.simpleMessage(
      "Anyone with the link can view",
    ),
    "app_feedback": MessageLookupByLibrary.simpleMessage(
      "Feedback for NoteX app",
    ),
    "app_store": MessageLookupByLibrary.simpleMessage(
      "review on the App Store",
    ),
    "appearance": MessageLookupByLibrary.simpleMessage("Appearance"),
    "appreciate_cooperation": MessageLookupByLibrary.simpleMessage(
      "This information will help our support team quickly identify and address your specific issue. We appreciate your cooperation in improving NoteX for everyone.",
    ),
    "appreciate_cooperation2": MessageLookupByLibrary.simpleMessage(
      "This helps us investigate and resolve your issue more effectively.",
    ),
    "appreciate_cooperation3": MessageLookupByLibrary.simpleMessage(
      "Thank you for using and trusting NoteX AI!",
    ),
    "are_you_sure": MessageLookupByLibrary.simpleMessage("One-time offer"),
    "ask_anything": MessageLookupByLibrary.simpleMessage("Ask anything ..."),
    "assist_faster": MessageLookupByLibrary.simpleMessage(
      "To help us assist you faster:",
    ),
    "assistant": MessageLookupByLibrary.simpleMessage("assistant"),
    "at_your_pace": MessageLookupByLibrary.simpleMessage("an A+"),
    "audio": MessageLookupByLibrary.simpleMessage("Audio"),
    "audio_file": MessageLookupByLibrary.simpleMessage("Audio File"),
    "audio_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*Audio is temporary - Save before closing",
    ),
    "audio_length_err": MessageLookupByLibrary.simpleMessage(
      "Audio file exceeds maximum length. Please upload a shorter file.",
    ),
    "audio_length_limit": MessageLookupByLibrary.simpleMessage(
      "Audio Length Limit",
    ),
    "audio_process_err": MessageLookupByLibrary.simpleMessage(
      "Unable to process audio file. Please try again with a different file.",
    ),
    "audio_recording_ai_notes_daily": MessageLookupByLibrary.simpleMessage(
      "3 Audio & Recording AI Notes Daily*",
    ),
    "audio_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "Audio to AI Notes",
    ),
    "audio_upload_note": MessageLookupByLibrary.simpleMessage("Audio Upload"),
    "audio_video_not_save": MessageLookupByLibrary.simpleMessage(
      "The audio/video will not be saved in the app. If you exit now, all your changes will be lost.",
    ),
    "auto": MessageLookupByLibrary.simpleMessage("Auto"),
    "auto_detect": MessageLookupByLibrary.simpleMessage("Auto detect"),
    "auto_generate_slides": MessageLookupByLibrary.simpleMessage(
      "Generate engaging slides instantly",
    ),
    "auto_renew_after_trial": MessageLookupByLibrary.simpleMessage(
      "Auto-renews after trial • Cancel anytime",
    ),
    "auto_renewable_after_trial": MessageLookupByLibrary.simpleMessage(
      "Auto-renewable after trial. Cancel anytime",
    ),
    "auto_renewal": MessageLookupByLibrary.simpleMessage(
      "Auto-renewal, cancel anytime",
    ),
    "available_credits": MessageLookupByLibrary.simpleMessage(
      "Available Credits",
    ),
    "available_transcript": MessageLookupByLibrary.simpleMessage(
      "Transcript will be available after note is successfully created!",
    ),
    "back_content": MessageLookupByLibrary.simpleMessage(" points"),
    "background_style": MessageLookupByLibrary.simpleMessage(
      "Background Style",
    ),
    "balanced": MessageLookupByLibrary.simpleMessage("Balanced"),
    "balanced_description": MessageLookupByLibrary.simpleMessage(
      "Main ideas with context ",
    ),
    "basic": MessageLookupByLibrary.simpleMessage("Basic Plan"),
    "basic_features": MessageLookupByLibrary.simpleMessage("Basic AI features"),
    "beta": MessageLookupByLibrary.simpleMessage("Beta"),
    "between_concepts": MessageLookupByLibrary.simpleMessage(
      "Connect the dots between concepts",
    ),
    "black_friday_sale": MessageLookupByLibrary.simpleMessage(
      "Merry Christmas Sale!",
    ),
    "blurred_output_image": MessageLookupByLibrary.simpleMessage(
      "Style generation failed! Please choose a different style or change the image!",
    ),
    "body_error_document_upload": MessageLookupByLibrary.simpleMessage(
      "There was an issue processing your document. Please go to the app and try again.",
    ),
    "body_error_note_document": MessageLookupByLibrary.simpleMessage(
      "There was an issue processing your document. Please go to the app and try again.",
    ),
    "body_error_note_recording": MessageLookupByLibrary.simpleMessage(
      "There was an issue processing your recording. Please go to the app and try again.",
    ),
    "body_error_note_upload": MessageLookupByLibrary.simpleMessage(
      "There was an issue processing your upload audio. Please go to the app and try again.",
    ),
    "body_error_note_web": MessageLookupByLibrary.simpleMessage(
      "There was an issue processing your web link. Please go to the app and try again.",
    ),
    "body_error_note_youtube": MessageLookupByLibrary.simpleMessage(
      "There was an issue processing your youtube link. Please go to the app and try again.",
    ),
    "body_success_note": MessageLookupByLibrary.simpleMessage(
      "Your AI note is now ready for review.",
    ),
    "bonus_credits_for_new_referred_friends_only":
        MessageLookupByLibrary.simpleMessage(
          "Bonus credits for new referred friends only",
        ),
    "boost_comprehension": MessageLookupByLibrary.simpleMessage(
      "Boost comprehension and retention",
    ),
    "boost_comprehension2": MessageLookupByLibrary.simpleMessage(
      "Boost comprehension",
    ),
    "boost_flashcards_quizzes": MessageLookupByLibrary.simpleMessage(
      "Boost your understanding with AI-generated flashcards and quizzes",
    ),
    "boost_knowledge": MessageLookupByLibrary.simpleMessage("Connect the dots"),
    "boost_knowledge_retention": MessageLookupByLibrary.simpleMessage(
      "Connect the dots between",
    ),
    "both_you_friends_receive_usage_credits":
        MessageLookupByLibrary.simpleMessage(
          "Both you and your friends will receive usage credits.",
        ),
    "brief_service_disruption": MessageLookupByLibrary.simpleMessage(
      "Brief service disruption. Please retry shortly. Get live status updates on Discord!",
    ),
    "business_uses": MessageLookupByLibrary.simpleMessage("Business Uses"),
    "button_below": MessageLookupByLibrary.simpleMessage(
      "button below or choose\na specific content input type to get started",
    ),
    "buy_one_forever": MessageLookupByLibrary.simpleMessage(
      "Buy Once. Unlock Max Productivity Forever.",
    ),
    "by_subscribing": MessageLookupByLibrary.simpleMessage(
      "By subscribing you agree to",
    ),
    "by_taping_continue": MessageLookupByLibrary.simpleMessage(
      "By continuing, you agree to our",
    ),
    "by_tapping_started": MessageLookupByLibrary.simpleMessage(
      "By tapping the “Get Started” button, you agree to our",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("Camera"),
    "camera_access": MessageLookupByLibrary.simpleMessage(
      "\"NoteX\" Would like to Access the Camera",
    ),
    "camera_permission": MessageLookupByLibrary.simpleMessage(
      "Camera Access Required",
    ),
    "camera_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "The app needs camera access to take photos. Please grant permission in Settings.",
    ),
    "can_improve": MessageLookupByLibrary.simpleMessage(
      "What could we improve?",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cannot_create_pdf_file_from_image": MessageLookupByLibrary.simpleMessage(
      "Cannot create PDF file from image",
    ),
    "cannot_extract_text_from_pdf": MessageLookupByLibrary.simpleMessage(
      "Unable to read the document. We couldn\'t extract any text from this document. This usually happens with scanned documents or image-only PDFs.",
    ),
    "card": MessageLookupByLibrary.simpleMessage("Card"),
    "card_count": MessageLookupByLibrary.simpleMessage("Card Count"),
    "card_difficulty": MessageLookupByLibrary.simpleMessage("Card Difficulty"),
    "change": MessageLookupByLibrary.simpleMessage("Change"),
    "change_plan": MessageLookupByLibrary.simpleMessage("Change Plan"),
    "chaos_into_clarity": MessageLookupByLibrary.simpleMessage(
      "chaos into clarity",
    ),
    "characters": MessageLookupByLibrary.simpleMessage("characters"),
    "chat_empty": MessageLookupByLibrary.simpleMessage("Chat empty"),
    "chat_topic_temporary_stored": MessageLookupByLibrary.simpleMessage(
      "Temporary session, use \"Save Chat\" to keep",
    ),
    "check_if_you": MessageLookupByLibrary.simpleMessage(
      "Check if you are signed into the correct Google account",
    ),
    "check_update": MessageLookupByLibrary.simpleMessage(
      "New version available",
    ),
    "child_detected": MessageLookupByLibrary.simpleMessage(
      "Child detected. Upload a different image.",
    ),
    "choose_your_note": MessageLookupByLibrary.simpleMessage(
      "Choose your NoteX",
    ),
    "choose_your_note_experience": MessageLookupByLibrary.simpleMessage(
      "Choose your NoteX experience",
    ),
    "click_create_podcast": MessageLookupByLibrary.simpleMessage(
      "Click \'Create Podcast\' to turn your note into engaging audio",
    ),
    "click_create_short": MessageLookupByLibrary.simpleMessage(
      "Click \'Create Short\' to turn your note into engaging shorts ",
    ),
    "click_create_slide": MessageLookupByLibrary.simpleMessage(
      "Click \'Create Slideshow\' to visualize your note as a presentation",
    ),
    "click_start_flashcard": MessageLookupByLibrary.simpleMessage(
      "Click \'Create Flashcards\' to generate flashcard sets based on the transcript. You can create multiple sets.",
    ),
    "click_start_mindmap": MessageLookupByLibrary.simpleMessage(
      "Click \'Create Mindmap\' to generate a set of mindmap based on the transcript",
    ),
    "click_start_quiz": MessageLookupByLibrary.simpleMessage(
      "Click \'Create Quizzes\' to generate question sets based on the transcript. You can create multiple sets.",
    ),
    "click_to_flip": MessageLookupByLibrary.simpleMessage("Click to flip"),
    "coming_soon": MessageLookupByLibrary.simpleMessage("Coming soon"),
    "community": MessageLookupByLibrary.simpleMessage("Community"),
    "community_feedback": MessageLookupByLibrary.simpleMessage(
      "Community & Feedback",
    ),
    "comprehensive": MessageLookupByLibrary.simpleMessage("Comprehensive"),
    "comprehensive_description": MessageLookupByLibrary.simpleMessage(
      "Detailed coverage with supporting points",
    ),
    "congratulations": MessageLookupByLibrary.simpleMessage("Congratulations!"),
    "connect_friends": MessageLookupByLibrary.simpleMessage(
      "Easily import shared note links from friends",
    ),
    "connection_fail": MessageLookupByLibrary.simpleMessage("Connection Fail!"),
    "connection_timeout": MessageLookupByLibrary.simpleMessage(
      "Connection timeout. Please check your internet connection and try again.",
    ),
    "contact_support": MessageLookupByLibrary.simpleMessage("Contact Support"),
    "content_account_trial": m0,
    "content_button_flashcard": MessageLookupByLibrary.simpleMessage(
      "Create Flashcards",
    ),
    "content_button_mindmap": MessageLookupByLibrary.simpleMessage(
      "Create Mindmap",
    ),
    "content_button_quiz": MessageLookupByLibrary.simpleMessage(
      "Create Quizzes",
    ),
    "content_button_summary": MessageLookupByLibrary.simpleMessage(
      "Generate Summary",
    ),
    "content_camera_access": MessageLookupByLibrary.simpleMessage(
      "NoteX needs access to your camera to capture, recognize, and digitize texts from images",
    ),
    "content_delete_note": MessageLookupByLibrary.simpleMessage(
      "You will not be able to recover them afterwards",
    ),
    "content_delete_note_detail": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to remove this note?",
    ),
    "content_delete_reminder": MessageLookupByLibrary.simpleMessage(
      "Are you certain you want to remove this reminder?",
    ),
    "content_discard_changes": MessageLookupByLibrary.simpleMessage(
      "Leaving will stop the recording and discard all changes.",
    ),
    "content_discard_changes_image": MessageLookupByLibrary.simpleMessage(
      "Closing will discard the photos you\'ve captured",
    ),
    "content_discard_changes_note": MessageLookupByLibrary.simpleMessage(
      "This action will discard all changes, and they cannot be undone.",
    ),
    "content_discard_changes_reminder": MessageLookupByLibrary.simpleMessage(
      "Leaving will close the reminder notification and discard all changes.",
    ),
    "content_empty_flashcard": MessageLookupByLibrary.simpleMessage(
      "Automated summary will appear here after the meeting is finished.",
    ),
    "content_empty_quiz": MessageLookupByLibrary.simpleMessage(
      "Automated summary will appear here after the meeting is finished.",
    ),
    "content_hour": MessageLookupByLibrary.simpleMessage("Hours of content to"),
    "content_hour_insight": MessageLookupByLibrary.simpleMessage(
      "Hours of content to insights",
    ),
    "content_minute_left": MessageLookupByLibrary.simpleMessage(
      "Your recordings will be saved locally without AI transcriptions and summarization if the recording session is over this free usage left this week. You can remove all limits by going Pro.",
    ),
    "content_payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Thank you for your purchase. Your transaction has been successfully processed.",
    ),
    "content_quarter_01": MessageLookupByLibrary.simpleMessage(
      "Unlimited AI Notes from Recordings, File Uploads, YouTube Links.",
    ),
    "content_quarter_02": MessageLookupByLibrary.simpleMessage(
      "Unlimited AI Chat, Mind Map, Flashcards, Quiz, Note Sharing.",
    ),
    "content_save_changes": MessageLookupByLibrary.simpleMessage(
      "This action will save all changes, and they will be permanently applied.",
    ),
    "continue_3_day": MessageLookupByLibrary.simpleMessage(
      "Continue 3-day Free Trial",
    ),
    "continue_button": MessageLookupByLibrary.simpleMessage("Continue"),
    "continue_with_apple": MessageLookupByLibrary.simpleMessage(
      "Continue with Apple",
    ),
    "continue_with_email": MessageLookupByLibrary.simpleMessage(
      "Continue with Email",
    ),
    "continue_with_google": MessageLookupByLibrary.simpleMessage(
      "Continue with Google",
    ),
    "copied_to_clipboard": MessageLookupByLibrary.simpleMessage(
      "Copied to clipboard",
    ),
    "copy": MessageLookupByLibrary.simpleMessage("Copy"),
    "copy_your_referral_code": MessageLookupByLibrary.simpleMessage(
      "Copy your referral code.",
    ),
    "correct": MessageLookupByLibrary.simpleMessage("Correct"),
    "craft_visual_from_every_note": MessageLookupByLibrary.simpleMessage(
      "Transform your notes into slides",
    ),
    "craft_visual_stories": MessageLookupByLibrary.simpleMessage(
      "Transform your notes",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Create"),
    "create_folder": MessageLookupByLibrary.simpleMessage("Create Folder"),
    "create_lecture": MessageLookupByLibrary.simpleMessage(
      "Create concise summaries of your lectures",
    ),
    "create_new_folder": MessageLookupByLibrary.simpleMessage(
      "Create New Folder",
    ),
    "create_note_successfully": MessageLookupByLibrary.simpleMessage(
      "Note is successfully created!",
    ),
    "create_notes": MessageLookupByLibrary.simpleMessage("Create AI notes.."),
    "create_podcast": MessageLookupByLibrary.simpleMessage("Create Podcast"),
    "create_reminder": MessageLookupByLibrary.simpleMessage("Create Reminder"),
    "create_select_a_language": MessageLookupByLibrary.simpleMessage(
      "Select a language",
    ),
    "create_short": MessageLookupByLibrary.simpleMessage("Create Short"),
    "create_shorts": MessageLookupByLibrary.simpleMessage("Create Shorts"),
    "create_slide": MessageLookupByLibrary.simpleMessage("Create Slideshow"),
    "creating_note": MessageLookupByLibrary.simpleMessage("Creating note ..."),
    "creating_quiz": MessageLookupByLibrary.simpleMessage(
      "Creating quiz questions",
    ),
    "credit": MessageLookupByLibrary.simpleMessage("Credit"),
    "credits": MessageLookupByLibrary.simpleMessage("Credits"),
    "credits_can_be_used_to_create_notes_and_access_the_features_within_them":
        MessageLookupByLibrary.simpleMessage(
          "Credits can be used to create notes and access the features within them. If your subscription expires, you can use credits to continue performing actions.",
        ),
    "credits_earned": MessageLookupByLibrary.simpleMessage("Credits Earned"),
    "credits_premium_features": MessageLookupByLibrary.simpleMessage(
      "credits & premium!",
    ),
    "credits_used": MessageLookupByLibrary.simpleMessage("Credits Used"),
    "current_plan": MessageLookupByLibrary.simpleMessage("Current Plan"),
    "custom_note_tabs": MessageLookupByLibrary.simpleMessage(
      "Customize Note Tabs",
    ),
    "customize_note_tabs": MessageLookupByLibrary.simpleMessage(
      "Customize Note Tabs",
    ),
    "customize_your_note_view": MessageLookupByLibrary.simpleMessage(
      "Customize Your Note View",
    ),
    "daily_10": MessageLookupByLibrary.simpleMessage("10 daily"),
    "daily_3": MessageLookupByLibrary.simpleMessage("3 daily"),
    "daily_5": MessageLookupByLibrary.simpleMessage("5 daily"),
    "daily_rewards_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Daily rewards limit reached. Try again tomorrow!",
    ),
    "daily_shorts_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Daily Shorts Limit Reached (Beta - Early Access)",
    ),
    "daily_shorts_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "You\'ve used up all Shorts generations for today. This beta feature has daily limits to ensure stable service. \nCome back tomorrow to create more AI short videos!",
    ),
    "daily_slideshow_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Daily Slideshow Limit Reached",
    ),
    "daily_slideshow_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "You\'ve used up all Slideshow generations for today. This beta feature has daily limits to ensure stable performance. Come back tomorrow to create more AI-powered slideshows!",
    ),
    "dark": MessageLookupByLibrary.simpleMessage("Dark"),
    "data": MessageLookupByLibrary.simpleMessage("data"),
    "day_free_trial_access_all_features": MessageLookupByLibrary.simpleMessage(
      "7-day free trial to access all features, then just ",
    ),
    "days": MessageLookupByLibrary.simpleMessage("Days"),
    "db_err": MessageLookupByLibrary.simpleMessage(
      "Database error. Please try again later.",
    ),
    "deals_left_at_this_price": MessageLookupByLibrary.simpleMessage(
      "lifetime deals left at this price",
    ),
    "decline_free_trial": MessageLookupByLibrary.simpleMessage(
      "Decline free trial",
    ),
    "default_error": MessageLookupByLibrary.simpleMessage(
      "Something went wrong! Please try again!",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "delete_account": MessageLookupByLibrary.simpleMessage("Delete account"),
    "delete_account_detail": MessageLookupByLibrary.simpleMessage(
      "This action cannot be undone. Deleting your account will permanently remove: All your notes and recordings",
    ),
    "delete_all_note": MessageLookupByLibrary.simpleMessage(
      "Delete all notes in folder",
    ),
    "delete_folder": MessageLookupByLibrary.simpleMessage("Delete Folder"),
    "delete_note": MessageLookupByLibrary.simpleMessage("Delete this note?"),
    "delete_note_item": MessageLookupByLibrary.simpleMessage("Delete Note"),
    "delete_recording": MessageLookupByLibrary.simpleMessage(
      "Delete Recording",
    ),
    "delete_recording_confirmation": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete",
    ),
    "delete_recording_setting_confirmation": MessageLookupByLibrary.simpleMessage(
      "The audio file will be permanently deleted from your device. This action cannot be undone.",
    ),
    "delete_reminder": MessageLookupByLibrary.simpleMessage("Delete reminder?"),
    "delete_success": MessageLookupByLibrary.simpleMessage(
      "The note has been successfully deleted.",
    ),
    "delete_this_folder": MessageLookupByLibrary.simpleMessage(
      "Delete this folder?",
    ),
    "delete_this_item": MessageLookupByLibrary.simpleMessage(
      "Delete this item?",
    ),
    "deselect": MessageLookupByLibrary.simpleMessage("Deselect"),
    "detail_unlimited_ai_summaries": MessageLookupByLibrary.simpleMessage(
      "Unlimited AI Notes from Recordings, Audio files, Documents, and YouTube videos",
    ),
    "developing_quizzes": MessageLookupByLibrary.simpleMessage(
      "Developing quizzes..",
    ),
    "discard": MessageLookupByLibrary.simpleMessage("Discard"),
    "discard_changes": MessageLookupByLibrary.simpleMessage("Discard changes?"),
    "dissatisfied": MessageLookupByLibrary.simpleMessage(
      "Thank you for your feedback. Your input helps us improve our product results, and we will work to make your experience better next time. Thank you very much!",
    ),
    "doc": MessageLookupByLibrary.simpleMessage("Doc"),
    "document": MessageLookupByLibrary.simpleMessage(
      "Upload Document (Coming Soon)",
    ),
    "document_available": MessageLookupByLibrary.simpleMessage(
      "Document will be available after note is successfully created!",
    ),
    "document_exceed_limit": MessageLookupByLibrary.simpleMessage(
      "The file exceeds 20MB. Please select a smaller file.",
    ),
    "document_limit": MessageLookupByLibrary.simpleMessage(
      "Document Upload Limit",
    ),
    "document_limit_message": MessageLookupByLibrary.simpleMessage(
      "Free users can summarize 1 Document per day.",
    ),
    "document_note": MessageLookupByLibrary.simpleMessage("Document Note"),
    "document_tab": MessageLookupByLibrary.simpleMessage("Document"),
    "document_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "Document to AI Notes",
    ),
    "document_type": MessageLookupByLibrary.simpleMessage(
      "Supported file types: .pdf, .doc, .docx, .txt, .md",
    ),
    "document_upload_note": MessageLookupByLibrary.simpleMessage(
      "Document upload",
    ),
    "document_webview_loading_message": MessageLookupByLibrary.simpleMessage(
      "Loading document content...",
    ),
    "done_button_label": MessageLookupByLibrary.simpleMessage("Done"),
    "donotallow": MessageLookupByLibrary.simpleMessage("Don\'t Allow"),
    "double_the_benefits": MessageLookupByLibrary.simpleMessage(
      "Double the Benefits!",
    ),
    "download_audio_file": MessageLookupByLibrary.simpleMessage(
      "Share audio file",
    ),
    "download_sucess": MessageLookupByLibrary.simpleMessage("Donwload success"),
    "duration": MessageLookupByLibrary.simpleMessage("Duration"),
    "each_ai_note_generation_uses_1_credit":
        MessageLookupByLibrary.simpleMessage(
          "Each AI note generation uses 1 credit",
        ),
    "each_referral_earns": MessageLookupByLibrary.simpleMessage(
      "Each referral earns",
    ),
    "early_access": MessageLookupByLibrary.simpleMessage(
      "Early Access to \nFuture Features",
    ),
    "early_supporters_exclusive_offer": MessageLookupByLibrary.simpleMessage(
      "Early supporters exclusive offer",
    ),
    "easily_import_shared_note_link": MessageLookupByLibrary.simpleMessage(
      "Easily import shared note links from friends",
    ),
    "easy": MessageLookupByLibrary.simpleMessage("Easy"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "edit_folder": MessageLookupByLibrary.simpleMessage("Edit Folder"),
    "edit_folder_name": MessageLookupByLibrary.simpleMessage(
      "Enter the folder name",
    ),
    "edit_name": MessageLookupByLibrary.simpleMessage("Edit Name"),
    "edit_note": MessageLookupByLibrary.simpleMessage("Edit Note"),
    "edit_notes": MessageLookupByLibrary.simpleMessage("Edit Note"),
    "edit_reminder": MessageLookupByLibrary.simpleMessage("Edit Reminder"),
    "edit_transcript": MessageLookupByLibrary.simpleMessage("Edit Transcript"),
    "edit_transcript_json_fail": MessageLookupByLibrary.simpleMessage(
      "Timestamped transcript edit fail. Please try again.",
    ),
    "edit_transcript_json_success": MessageLookupByLibrary.simpleMessage(
      "Timestamped transcript has been successfully edited",
    ),
    "email_invalid": MessageLookupByLibrary.simpleMessage(
      "The email address is not valid.",
    ),
    "email_sent": MessageLookupByLibrary.simpleMessage("Check your inbox"),
    "email_sent_success": MessageLookupByLibrary.simpleMessage(
      "We\'ve sent you a magic link to sign in.\nClick the link in your email to continue.",
    ),
    "enable_free": MessageLookupByLibrary.simpleMessage("Enable Free Trial"),
    "enables_swap": MessageLookupByLibrary.simpleMessage(
      "Enables image reordering by selection and swapping",
    ),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enter_card_count": MessageLookupByLibrary.simpleMessage(
      "Enter card count",
    ),
    "enter_email": MessageLookupByLibrary.simpleMessage(
      "Enter your email address...",
    ),
    "enter_feedback": MessageLookupByLibrary.simpleMessage(
      "Enter your feedback",
    ),
    "enter_folder_name": MessageLookupByLibrary.simpleMessage(
      "Fill the name folder",
    ),
    "enter_new_name": MessageLookupByLibrary.simpleMessage("Enter new name"),
    "enter_quiz_count": MessageLookupByLibrary.simpleMessage(
      "Enter quiz count",
    ),
    "enter_referral_code": MessageLookupByLibrary.simpleMessage(
      "Enter Referral Code",
    ),
    "enter_slide_count": MessageLookupByLibrary.simpleMessage(
      "Enter Slide Count",
    ),
    "enter_title": MessageLookupByLibrary.simpleMessage("Enter title"),
    "enter_valid_email": MessageLookupByLibrary.simpleMessage(
      "Please enter a valid email address",
    ),
    "error": MessageLookupByLibrary.simpleMessage("Error"),
    "error_connection": MessageLookupByLibrary.simpleMessage(
      "Something went wrong with the connection.\nPlease try again",
    ),
    "error_convert_image": MessageLookupByLibrary.simpleMessage(
      "Error converting ui.Image to image.Image",
    ),
    "error_logging_in": MessageLookupByLibrary.simpleMessage(
      "Connect to Internet",
    ),
    "esc": MessageLookupByLibrary.simpleMessage("Esc"),
    "essential": MessageLookupByLibrary.simpleMessage("Essential"),
    "essential_lifetime": MessageLookupByLibrary.simpleMessage(
      "Essential Lifetime",
    ),
    "essential_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Essential Lifetime Access",
    ),
    "evening_content": MessageLookupByLibrary.simpleMessage(
      "Reflect, capture, grow",
    ),
    "evening_content_2": MessageLookupByLibrary.simpleMessage(
      "Today\'s insights preserved",
    ),
    "evening_content_3": MessageLookupByLibrary.simpleMessage(
      "Tomorrow begins with today\'s notes",
    ),
    "evening_content_4": MessageLookupByLibrary.simpleMessage(
      "Thoughts organized, mind at ease",
    ),
    "evening_content_5": MessageLookupByLibrary.simpleMessage(
      "Save now, thank yourself later",
    ),
    "evening_content_6": MessageLookupByLibrary.simpleMessage(
      "Progress preserved",
    ),
    "every_note_you_take": MessageLookupByLibrary.simpleMessage("into slides"),
    "experience": MessageLookupByLibrary.simpleMessage("experience"),
    "export": MessageLookupByLibrary.simpleMessage("Export"),
    "export_as": MessageLookupByLibrary.simpleMessage("Export as"),
    "export_audio": MessageLookupByLibrary.simpleMessage("Export Audio File"),
    "export_failed": MessageLookupByLibrary.simpleMessage(
      "Export failed. Please try again later.",
    ),
    "export_flashcard": MessageLookupByLibrary.simpleMessage(
      "Export Flashcard",
    ),
    "export_mind_map": MessageLookupByLibrary.simpleMessage(
      "Export Mindmap as",
    ),
    "export_pdf": MessageLookupByLibrary.simpleMessage("Export Summary"),
    "export_quiz": MessageLookupByLibrary.simpleMessage("Export Quiz"),
    "export_to_pdf_share_notes": MessageLookupByLibrary.simpleMessage(
      "Export to PDF & Share Notes",
    ),
    "export_transcript": MessageLookupByLibrary.simpleMessage(
      "Export Transcript",
    ),
    "export_video": MessageLookupByLibrary.simpleMessage("Export Video"),
    "extracting_text_from_document": MessageLookupByLibrary.simpleMessage(
      "Extracting text from document",
    ),
    "fail": MessageLookupByLibrary.simpleMessage("Fail"),
    "fail_create_pdf": MessageLookupByLibrary.simpleMessage(
      "Failed to create PDF file",
    ),
    "fail_to_load_document": MessageLookupByLibrary.simpleMessage(
      "Fail to load document!",
    ),
    "fail_to_load_video": MessageLookupByLibrary.simpleMessage(
      "Failed to load video",
    ),
    "failed_get_anonymous_user": MessageLookupByLibrary.simpleMessage(
      "Failed to get jwt anonymous user",
    ),
    "failed_to_delete_recording": MessageLookupByLibrary.simpleMessage(
      "Failed to delete recording",
    ),
    "failed_to_load_slideshow": MessageLookupByLibrary.simpleMessage(
      "Failed to load the slideshow from the system. Retry for a better experience.",
    ),
    "failed_to_save_file": MessageLookupByLibrary.simpleMessage(
      "Failed to save file",
    ),
    "feedback": MessageLookupByLibrary.simpleMessage("Feedback"),
    "file_import": MessageLookupByLibrary.simpleMessage("File Import"),
    "file_save_success": MessageLookupByLibrary.simpleMessage(
      "File saved successfully",
    ),
    "file_size_err": MessageLookupByLibrary.simpleMessage(
      "File size exceeds the limit. Please upload a smaller file.",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filter"),
    "filter_and_sort": MessageLookupByLibrary.simpleMessage("Filter & Sort"),
    "finalizing": MessageLookupByLibrary.simpleMessage("Finalizing.."),
    "find_and_replace": MessageLookupByLibrary.simpleMessage(
      "Find and Replace",
    ),
    "flash_card_gen_success": MessageLookupByLibrary.simpleMessage(
      "FlashCard generated success",
    ),
    "flash_card_iap": MessageLookupByLibrary.simpleMessage("Flashcard Set"),
    "flashcard": MessageLookupByLibrary.simpleMessage("Flashcards"),
    "flashcard_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Flashcard set not found",
    ),
    "flashcard_sets": MessageLookupByLibrary.simpleMessage("Flashcard Sets"),
    "flashcards": MessageLookupByLibrary.simpleMessage("Flashcards"),
    "flashcards_for": MessageLookupByLibrary.simpleMessage("Flashcards for"),
    "focus_on": MessageLookupByLibrary.simpleMessage("Focus on What Matters"),
    "folder": MessageLookupByLibrary.simpleMessage("Folder"),
    "folder_move_success": MessageLookupByLibrary.simpleMessage(
      "Folder moved successfully",
    ),
    "follow_steps_to_get_rewarded": MessageLookupByLibrary.simpleMessage(
      "Follow these steps to get rewarded",
    ),
    "for_unlimited_experiences": MessageLookupByLibrary.simpleMessage(
      "for unlimited experiences.",
    ),
    "free": MessageLookupByLibrary.simpleMessage("Free"),
    "free_30_minutes": MessageLookupByLibrary.simpleMessage(
      "Free: 30 minutes per file",
    ),
    "free_messages": MessageLookupByLibrary.simpleMessage("free messages"),
    "free_recording_limit": MessageLookupByLibrary.simpleMessage(
      "Free Recording Limit",
    ),
    "free_recording_limit_details": MessageLookupByLibrary.simpleMessage(
      "You have %s-minute free transcriptions and AI summaries remaining this week.",
    ),
    "free_trial": MessageLookupByLibrary.simpleMessage("Free trial"),
    "free_updates": MessageLookupByLibrary.simpleMessage(
      "Free lifetime updates & enhancements",
    ),
    "free_usage": MessageLookupByLibrary.simpleMessage("Free Usage - Weekly"),
    "free_user_audio": MessageLookupByLibrary.simpleMessage(
      "Free users can transcribe and summarize audio up to 30 minutes in length",
    ),
    "free_user_can": MessageLookupByLibrary.simpleMessage(
      "Free users can summarize 1 YouTube video (max 30 min) per day.",
    ),
    "friendly": MessageLookupByLibrary.simpleMessage("Friendly"),
    "friendly_description": MessageLookupByLibrary.simpleMessage(
      "Conversational with emoji",
    ),
    "front_content": MessageLookupByLibrary.simpleMessage("You\'ve scored "),
    "future_features": MessageLookupByLibrary.simpleMessage(
      "Future features could have usage allowances",
    ),
    "gen_ai": MessageLookupByLibrary.simpleMessage("Generating AI ..."),
    "gen_ai_voice": MessageLookupByLibrary.simpleMessage("Generating AI voice"),
    "gen_quiz_bgr": MessageLookupByLibrary.simpleMessage(
      "Generating quiz background",
    ),
    "generate_audio": MessageLookupByLibrary.simpleMessage("Generate Audio"),
    "generate_content": MessageLookupByLibrary.simpleMessage(
      "Generate smart summaries of YouTube content",
    ),
    "generate_note_fail": MessageLookupByLibrary.simpleMessage(
      "Fail to generate AI notes!",
    ),
    "generate_shorts_step_1": MessageLookupByLibrary.simpleMessage(
      "Crafting your story...",
    ),
    "generate_shorts_step_2": MessageLookupByLibrary.simpleMessage(
      "Adding perfect voice...",
    ),
    "generate_shorts_step_3": MessageLookupByLibrary.simpleMessage(
      "Making it look amazing! This video will be worth sharing #NoteXAI",
    ),
    "generate_shorts_study_guides": MessageLookupByLibrary.simpleMessage(
      "Generate Shorts & Study Guides",
    ),
    "generate_transcript_notes": MessageLookupByLibrary.simpleMessage(
      "We\'ll generate a transcript, notes, and a study guide",
    ),
    "generate_video": MessageLookupByLibrary.simpleMessage("Generate Video"),
    "generating_ai_note": MessageLookupByLibrary.simpleMessage(
      "Generating AI notes",
    ),
    "generating_summary": MessageLookupByLibrary.simpleMessage(
      "Generating AI summary..",
    ),
    "get_fail": MessageLookupByLibrary.simpleMessage(
      "Fail to get quiz/flashcards/mindmap. Please try again!",
    ),
    "get_more_done": MessageLookupByLibrary.simpleMessage(
      "Get more done, stay",
    ),
    "get_more_done_stay_on_track": MessageLookupByLibrary.simpleMessage(
      "Get more done, stay on track",
    ),
    "get_now": MessageLookupByLibrary.simpleMessage("GET NOW"),
    "get_offer_now": MessageLookupByLibrary.simpleMessage("Get Offer Now"),
    "get_start": MessageLookupByLibrary.simpleMessage("Get Started"),
    "go_back": MessageLookupByLibrary.simpleMessage("Go back"),
    "go_email": MessageLookupByLibrary.simpleMessage("Go to Email"),
    "go_pro": MessageLookupByLibrary.simpleMessage("Go PRO"),
    "go_unlimited": MessageLookupByLibrary.simpleMessage("Go Unlimited!"),
    "good_afternoon": MessageLookupByLibrary.simpleMessage("Good Afternoon!"),
    "good_evening": MessageLookupByLibrary.simpleMessage("Good Evening!"),
    "good_morning": MessageLookupByLibrary.simpleMessage("Good Morning!"),
    "got_it": MessageLookupByLibrary.simpleMessage("Got it !"),
    "hard": MessageLookupByLibrary.simpleMessage("Hard"),
    "hello_welcome": MessageLookupByLibrary.simpleMessage("Welcome back 👋"),
    "help_legal": MessageLookupByLibrary.simpleMessage("Help & Legal"),
    "help_us_grow": MessageLookupByLibrary.simpleMessage("Help Us Grow!"),
    "hi": MessageLookupByLibrary.simpleMessage("Hi"),
    "hope_enjoy_app": MessageLookupByLibrary.simpleMessage(
      "Hope you enjoy our app and thanks for your support !",
    ),
    "hours": MessageLookupByLibrary.simpleMessage("Hours"),
    "how_will_you_use_notex": MessageLookupByLibrary.simpleMessage(
      "How will you use NoteX?",
    ),
    "http_failed": MessageLookupByLibrary.simpleMessage(
      "HTTP request failed. Please try again later.",
    ),
    "idea1": MessageLookupByLibrary.simpleMessage(
      "Sign in with your Google or Apple account if you haven\'t already",
    ),
    "idea2": MessageLookupByLibrary.simpleMessage(
      "Provide a brief description of what happened",
    ),
    "idea3": MessageLookupByLibrary.simpleMessage(
      "Include relevant details (device, OS version)",
    ),
    "idea4": MessageLookupByLibrary.simpleMessage(
      "Mention when the issue started occurring",
    ),
    "idea5": MessageLookupByLibrary.simpleMessage("Email us directly at"),
    "image": MessageLookupByLibrary.simpleMessage("Image"),
    "image_jpeg": MessageLookupByLibrary.simpleMessage("Image (.jpeg)"),
    "image_png": MessageLookupByLibrary.simpleMessage("Image (.png)"),
    "image_quality_too_low": MessageLookupByLibrary.simpleMessage(
      "Image quality too low. Use a higher quality image!",
    ),
    "image_too_large": MessageLookupByLibrary.simpleMessage(
      "Image too large. Upload one under 10MB.",
    ),
    "images_have_been_uploaded": m1,
    "import_note_links": MessageLookupByLibrary.simpleMessage(
      "Import Note Links",
    ),
    "import_notes": MessageLookupByLibrary.simpleMessage("Import Shared Notes"),
    "improve_responses": MessageLookupByLibrary.simpleMessage(
      "Your responses will help us improve",
    ),
    "initializing_camera": MessageLookupByLibrary.simpleMessage(
      "Initializing camera...",
    ),
    "insight_instantly": MessageLookupByLibrary.simpleMessage(
      "Hours of content to insights instantly",
    ),
    "insights_instantly": MessageLookupByLibrary.simpleMessage(
      "insights instantly",
    ),
    "instant_answers_from_your": MessageLookupByLibrary.simpleMessage(
      "Instant answers from your",
    ),
    "instant_answers_from_your_meeting_data":
        MessageLookupByLibrary.simpleMessage(
          "Instant answers from your meeting data",
        ),
    "instant_answers_meeting": MessageLookupByLibrary.simpleMessage(
      "Instant answers from your meeting",
    ),
    "instantly": MessageLookupByLibrary.simpleMessage("instantly"),
    "interactive_ai_flashcards": MessageLookupByLibrary.simpleMessage(
      "Interactive AI Mind Map",
    ),
    "interactive_flash": MessageLookupByLibrary.simpleMessage(
      "Interactive Flashcards",
    ),
    "interactive_flashcards": MessageLookupByLibrary.simpleMessage(
      "Unlimited interactive Flashcards, Mind maps",
    ),
    "interactive_flashcards_quiz": MessageLookupByLibrary.simpleMessage(
      "Interactive Flashcards & Quiz",
    ),
    "introduce_guidance": MessageLookupByLibrary.simpleMessage(
      "Thank you for reaching out. To help us investigate and resolve your issue more effectively, please follow these steps:",
    ),
    "introduce_guidance2": MessageLookupByLibrary.simpleMessage(
      "We understand how frustrating it can be when you\'re experiencing issues, especially if they involve your important notes or recordings. Our support team is ready to help resolve any problems you\'re facing, usually within 12 hours.",
    ),
    "inv_audio": MessageLookupByLibrary.simpleMessage(
      "Invalid audio file. Please upload a supported audio format.",
    ),
    "inv_yt_url": MessageLookupByLibrary.simpleMessage(
      "Invalid YouTube URL. Please provide a valid YouTube link.",
    ),
    "invalid_code": MessageLookupByLibrary.simpleMessage(
      "Invalid code. Try again.",
    ),
    "invalid_file_type": MessageLookupByLibrary.simpleMessage(
      "The uploaded file is in the wrong format. Please upload it again.",
    ),
    "invalid_token": MessageLookupByLibrary.simpleMessage("Invalid token"),
    "invite_friends": MessageLookupByLibrary.simpleMessage(
      "Invite friends – both get",
    ),
    "items": MessageLookupByLibrary.simpleMessage("Items"),
    "join_discord": MessageLookupByLibrary.simpleMessage("Join the Discord"),
    "join_noteX_ai_lets_level_up_together":
        MessageLookupByLibrary.simpleMessage(
          "Join NoteX AI and let\'s level up together!",
        ),
    "language": MessageLookupByLibrary.simpleMessage("Language"),
    "language_tip": MessageLookupByLibrary.simpleMessage(
      "Choose the main language in your audio for best results",
    ),
    "language_tip_1": MessageLookupByLibrary.simpleMessage(
      "Select main language for the best transcription results",
    ),
    "language_tip_2": MessageLookupByLibrary.simpleMessage(
      "For mixed conversation, please choose multi-language",
    ),
    "language_tip_3": MessageLookupByLibrary.simpleMessage(
      "Calls will auto-pause recording. Return to app to resume",
    ),
    "latest_ai_models": MessageLookupByLibrary.simpleMessage(
      "Latest AI models",
    ),
    "learn_faster_through": MessageLookupByLibrary.simpleMessage(
      "Learn faster through",
    ),
    "learn_faster_through_active_recall": MessageLookupByLibrary.simpleMessage(
      "Learn faster through active recall",
    ),
    "learn_smart": MessageLookupByLibrary.simpleMessage("Learn Smart "),
    "learn_unlimited": MessageLookupByLibrary.simpleMessage(
      "Learn Smart Go Unlimited!",
    ),
    "lecture_notes_study_materials": MessageLookupByLibrary.simpleMessage(
      "Lecture Notes & Study Materials",
    ),
    "let_ai_handle": MessageLookupByLibrary.simpleMessage(
      "Let AI handle the details",
    ),
    "let_note_ai": MessageLookupByLibrary.simpleMessage(
      "Let NoteX AI turn information",
    ),
    "let_start": MessageLookupByLibrary.simpleMessage("Let’s start"),
    "lets_create_your_first_ai_note": MessageLookupByLibrary.simpleMessage(
      "Let\'s create your first AI note!",
    ),
    "lifetime": MessageLookupByLibrary.simpleMessage("Lifetime Plan"),
    "lifetime_pro_access_level_up_together":
        MessageLookupByLibrary.simpleMessage("Level up together ✨"),
    "lifetime_setting": MessageLookupByLibrary.simpleMessage("lifetime"),
    "lifetime_spots_remaining": MessageLookupByLibrary.simpleMessage(
      "Lifetime spots remaining",
    ),
    "light": MessageLookupByLibrary.simpleMessage("Light"),
    "limited_notes": MessageLookupByLibrary.simpleMessage(
      "Limited notes per day",
    ),
    "limited_offer": MessageLookupByLibrary.simpleMessage("Limited Offer"),
    "limited_time": MessageLookupByLibrary.simpleMessage("Limited Time"),
    "limited_time_02": MessageLookupByLibrary.simpleMessage("LIMITED TIME"),
    "link": MessageLookupByLibrary.simpleMessage("Link"),
    "link_error": MessageLookupByLibrary.simpleMessage("Error with Link"),
    "link_expired": MessageLookupByLibrary.simpleMessage(
      "The email link has expired.",
    ),
    "link_invalid": MessageLookupByLibrary.simpleMessage(
      "The link you used is invalid, may have expired, or has already been used. Please request a new link and try again.",
    ),
    "loading": MessageLookupByLibrary.simpleMessage("Loading"),
    "loading_content": MessageLookupByLibrary.simpleMessage(
      "Loading content...",
    ),
    "local_recording": MessageLookupByLibrary.simpleMessage("Smart Recording"),
    "login_failed": MessageLookupByLibrary.simpleMessage("Login failed."),
    "login_info_1": MessageLookupByLibrary.simpleMessage(
      "Access your notes on any device",
    ),
    "login_info_2": MessageLookupByLibrary.simpleMessage(
      "Enterprise-grade security powered by AWS",
    ),
    "login_info_3": MessageLookupByLibrary.simpleMessage(
      "Your data stays private",
    ),
    "login_info_4": MessageLookupByLibrary.simpleMessage(
      "Access on Web at notexapp.com",
    ),
    "login_success": MessageLookupByLibrary.simpleMessage("Login Success!"),
    "login_title": MessageLookupByLibrary.simpleMessage(
      "Maximize Productivity, Everywhere!",
    ),
    "login_title_2": MessageLookupByLibrary.simpleMessage(
      "Introducing NoteX 2.0",
    ),
    "login_unsuccessful": MessageLookupByLibrary.simpleMessage(
      "Login unsuccessful. Try again or use another login method.",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Log Out"),
    "logout_detail": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to log out?",
    ),
    "logout_question_mark": MessageLookupByLibrary.simpleMessage("Log out?"),
    "loved_by": MessageLookupByLibrary.simpleMessage("Loved by "),
    "make_the_interface_feel_more_like_you": MessageLookupByLibrary.simpleMessage(
      "Make the interface feel more like you — with theme, font, and layout preferences at your fingertips.",
    ),
    "making_amazing": MessageLookupByLibrary.simpleMessage(
      "Making it look amazing! This quiz video will be worth sharing #NoteXAI",
    ),
    "manage_recordings": MessageLookupByLibrary.simpleMessage(
      "Manage Recordings",
    ),
    "map_all_together": MessageLookupByLibrary.simpleMessage(
      "Mapping all together",
    ),
    "markdown_md": MessageLookupByLibrary.simpleMessage("Markdown (.md)"),
    "max_30": MessageLookupByLibrary.simpleMessage(
      "You have a maximum of 30 questions.",
    ),
    "max_30_cards_per_set": MessageLookupByLibrary.simpleMessage(
      "Maximum of 30 Cards per Set",
    ),
    "max_30_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Maximum of 30 Questions per Set",
    ),
    "max_3_flashcard_sets": MessageLookupByLibrary.simpleMessage(
      "Maximum 3 Flashcard Sets",
    ),
    "max_3_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Maximum 3 Quiz Sets",
    ),
    "max_60_min_per_file": MessageLookupByLibrary.simpleMessage(
      "* max 60 min per file",
    ),
    "max_ai": MessageLookupByLibrary.simpleMessage("Max AI Transcription:"),
    "maybe_later": MessageLookupByLibrary.simpleMessage("Maybe later"),
    "medium": MessageLookupByLibrary.simpleMessage("Medium"),
    "meeting_data": MessageLookupByLibrary.simpleMessage("meeting data"),
    "migrating_your_notes": MessageLookupByLibrary.simpleMessage(
      "Migrating your notes...",
    ),
    "migration_complete": MessageLookupByLibrary.simpleMessage(
      "Migration complete!",
    ),
    "mind_map": MessageLookupByLibrary.simpleMessage("Mindmap"),
    "mind_map_gen_success": MessageLookupByLibrary.simpleMessage(
      "MindMap generated success",
    ),
    "mind_map_iap": MessageLookupByLibrary.simpleMessage("Mind Map"),
    "mind_map_study": MessageLookupByLibrary.simpleMessage(
      "Mind Map, \nStudy Guides",
    ),
    "minute_60_per_file": MessageLookupByLibrary.simpleMessage(
      "Essential: 60 minutes per file",
    ),
    "minute_free": MessageLookupByLibrary.simpleMessage(
      "You\'ve used all 30-minute free transcriptions and AI summaries for this week. Upgrade to Pro for unlimited access or wait until next week for your free quota to reset.",
    ),
    "minutes": MessageLookupByLibrary.simpleMessage("Minutes"),
    "minutes_free_left": MessageLookupByLibrary.simpleMessage(
      " Minutes Free Left",
    ),
    "minutes_remaining": MessageLookupByLibrary.simpleMessage(
      "minutes remaining",
    ),
    "mixed": MessageLookupByLibrary.simpleMessage("Mixed"),
    "month": MessageLookupByLibrary.simpleMessage("month"),
    "monthly": MessageLookupByLibrary.simpleMessage("Monthly"),
    "more_summarize": MessageLookupByLibrary.simpleMessage(
      "Summarize meetings, podcasts, tutorials, and more",
    ),
    "morning_content": MessageLookupByLibrary.simpleMessage(
      "Capture today\'s brilliance",
    ),
    "morning_content_2": MessageLookupByLibrary.simpleMessage(
      "Clear mind, clear path",
    ),
    "morning_content_3": MessageLookupByLibrary.simpleMessage(
      "Today\'s notes shape tomorrow",
    ),
    "morning_content_4": MessageLookupByLibrary.simpleMessage(
      "First thought, best thought",
    ),
    "morning_content_5": MessageLookupByLibrary.simpleMessage(
      "Begin with clarity",
    ),
    "morning_content_6": MessageLookupByLibrary.simpleMessage(
      "Ideas worth keeping",
    ),
    "most_popular": MessageLookupByLibrary.simpleMessage("Most popular"),
    "move": MessageLookupByLibrary.simpleMessage("Move"),
    "multi_language": MessageLookupByLibrary.simpleMessage("Multi-language"),
    "multiple_people_detected": MessageLookupByLibrary.simpleMessage(
      "Multiple people detected. Upload a single-person image!",
    ),
    "multiply_knowledge_with_friends": MessageLookupByLibrary.simpleMessage(
      "Multiply knowledge with friends",
    ),
    "my_notes": MessageLookupByLibrary.simpleMessage("My notes"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "network_error": MessageLookupByLibrary.simpleMessage(
      "Network error. Please check your internet connection and try again.",
    ),
    "neutral": MessageLookupByLibrary.simpleMessage("Neutral"),
    "neutral_description": MessageLookupByLibrary.simpleMessage(
      "Straightforward, factual presentation",
    ),
    "new_new": MessageLookupByLibrary.simpleMessage("NEW"),
    "new_note": MessageLookupByLibrary.simpleMessage("New note"),
    "new_recording": MessageLookupByLibrary.simpleMessage("New Recording - "),
    "newest_first": MessageLookupByLibrary.simpleMessage("Newest First"),
    "next_bill_date": m2,
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "no_generated": MessageLookupByLibrary.simpleMessage(
      "No quiz/flashcards generated. Tap to create now!",
    ),
    "no_input": MessageLookupByLibrary.simpleMessage(
      "No input provided. Please upload an audio file or enter a YouTube URL.",
    ),
    "no_internet": MessageLookupByLibrary.simpleMessage(
      "No internet connection",
    ),
    "no_internet_connection": MessageLookupByLibrary.simpleMessage(
      "No Internet connection!",
    ),
    "no_notes_found": MessageLookupByLibrary.simpleMessage(
      "No notes found under this filter.\nPlease reset the filter selection",
    ),
    "no_notes_in_folder": MessageLookupByLibrary.simpleMessage(
      "There are no notes in this folder.",
    ),
    "no_payment_now": MessageLookupByLibrary.simpleMessage("✓ No payment now"),
    "no_person_detected": MessageLookupByLibrary.simpleMessage(
      "No person detected. Upload an image with a person!",
    ),
    "no_recording_credit": MessageLookupByLibrary.simpleMessage(
      "Insufficient recording remaining usage. Please upgrade your plan.",
    ),
    "no_recordings": MessageLookupByLibrary.simpleMessage("No recordings"),
    "no_results_found": MessageLookupByLibrary.simpleMessage(
      "No results found for",
    ),
    "no_speech_detected": MessageLookupByLibrary.simpleMessage(
      "No Speech Detected",
    ),
    "no_summary": MessageLookupByLibrary.simpleMessage(
      "No summary available for this note.",
    ),
    "no_transcript": MessageLookupByLibrary.simpleMessage(
      "No transcript available for this note.",
    ),
    "no_upload_credit": MessageLookupByLibrary.simpleMessage(
      "Insufficient upload usage. Please upgrade your plan.",
    ),
    "no_url_provided": MessageLookupByLibrary.simpleMessage(
      "No export URL provided.",
    ),
    "no_voice_available": MessageLookupByLibrary.simpleMessage(
      "No voice available",
    ),
    "not_found_audio": MessageLookupByLibrary.simpleMessage(
      "Audio file not found",
    ),
    "not_open_mail": MessageLookupByLibrary.simpleMessage(
      "Could not open mail!",
    ),
    "not_open_web": MessageLookupByLibrary.simpleMessage("Could not open web!"),
    "not_summarized_note": MessageLookupByLibrary.simpleMessage(
      "Summary missing! Hit that button to get the AI to work👇",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "noteX": MessageLookupByLibrary.simpleMessage("NoteX"),
    "noteX_lifetime_essential": MessageLookupByLibrary.simpleMessage(
      "NoteX Essential Lifetime",
    ),
    "noteX_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "NoteX Pro Lifetime",
    ),
    "note_404": MessageLookupByLibrary.simpleMessage(
      "Note not found. Please check the note ID and try again.",
    ),
    "note_not_ready": MessageLookupByLibrary.simpleMessage(
      "Note is not ready for export. Please wait for processing to complete.",
    ),
    "note_reminders": MessageLookupByLibrary.simpleMessage("Note Reminders"),
    "note_sharing": MessageLookupByLibrary.simpleMessage("Note Sharing"),
    "note_tabs": MessageLookupByLibrary.simpleMessage("Note Tabs"),
    "note_taker": MessageLookupByLibrary.simpleMessage("#1 AI Notetaker"),
    "notes": MessageLookupByLibrary.simpleMessage("notes"),
    "notex_empty": MessageLookupByLibrary.simpleMessage("NoteX empty"),
    "notex_experience": MessageLookupByLibrary.simpleMessage(
      "your experience with NoteX",
    ),
    "nothing_restore": MessageLookupByLibrary.simpleMessage(
      "Nothing to restore",
    ),
    "noti_default_description": MessageLookupByLibrary.simpleMessage(
      "Get ready and start recording! 🚀",
    ),
    "noti_default_title": MessageLookupByLibrary.simpleMessage(
      "It\'s time to record",
    ),
    "noti_req_description": MessageLookupByLibrary.simpleMessage(
      "Notifications may include alerts, sounds, and icon badges. These can be configured in Settings.",
    ),
    "noti_req_title": MessageLookupByLibrary.simpleMessage(
      "‘NoteX’ Would Like to Send You Notifications",
    ),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "notifications_note_created": MessageLookupByLibrary.simpleMessage(
      "Your notes are successfully created",
    ),
    "notifications_note_ready": MessageLookupByLibrary.simpleMessage(
      "Notify when notes are ready",
    ),
    "nova_ai_assistant_mind_mapping": MessageLookupByLibrary.simpleMessage(
      "Nova AI Assistant & Mind Mapping",
    ),
    "nova_ai_chat": MessageLookupByLibrary.simpleMessage("Nova AI Chat"),
    "nova_chat": MessageLookupByLibrary.simpleMessage("Nova Chat"),
    "number_of_flash": MessageLookupByLibrary.simpleMessage(
      "Create Flashcards",
    ),
    "number_of_quiz": MessageLookupByLibrary.simpleMessage("Number of Quizzes"),
    "of_index": MessageLookupByLibrary.simpleMessage("of"),
    "of_user": MessageLookupByLibrary.simpleMessage(" of Users"),
    "offer_expires": MessageLookupByLibrary.simpleMessage("Offer expires "),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "oldest_first": MessageLookupByLibrary.simpleMessage("Oldest First"),
    "on_track": MessageLookupByLibrary.simpleMessage("on track"),
    "on_your_android": MessageLookupByLibrary.simpleMessage(
      "On your Android phone or tablet, open the Google Play Store",
    ),
    "onboarding_generate_audio_video_content":
        MessageLookupByLibrary.simpleMessage("Transform notes into"),
    "onboarding_generate_audio_video_full_content":
        MessageLookupByLibrary.simpleMessage(
          "Transform notes into engaging content",
        ),
    "onboarding_generate_audio_video_sub_content":
        MessageLookupByLibrary.simpleMessage("engaging content"),
    "onboarding_generate_audio_video_title":
        MessageLookupByLibrary.simpleMessage("Generate Audio & Video"),
    "once_in_a_lifetime_offer": MessageLookupByLibrary.simpleMessage(
      "Once-in-a-lifetime offer",
    ),
    "one_per_day": MessageLookupByLibrary.simpleMessage("1 per day"),
    "one_time_payment": MessageLookupByLibrary.simpleMessage(
      "one-time payment",
    ),
    "only": MessageLookupByLibrary.simpleMessage("Only"),
    "only_today": MessageLookupByLibrary.simpleMessage("Only Today"),
    "only_you_can_view_this_note": MessageLookupByLibrary.simpleMessage(
      "Only you can view this note",
    ),
    "oops_something_went_wrong": MessageLookupByLibrary.simpleMessage(
      "Oops!\nSomething went wrong",
    ),
    "open_now": MessageLookupByLibrary.simpleMessage("Open Now"),
    "open_youtube": MessageLookupByLibrary.simpleMessage("Open YouTube"),
    "opportunities": MessageLookupByLibrary.simpleMessage("opportunities"),
    "or": MessageLookupByLibrary.simpleMessage("or"),
    "or_upper": MessageLookupByLibrary.simpleMessage("Or"),
    "organize_assign_action_items": MessageLookupByLibrary.simpleMessage(
      "Organize & Assign Action Items",
    ),
    "organize_assign_items": MessageLookupByLibrary.simpleMessage(
      "Organize & Assign Action Items",
    ),
    "others": MessageLookupByLibrary.simpleMessage("Others"),
    "output_language": MessageLookupByLibrary.simpleMessage("Output Language"),
    "pace": MessageLookupByLibrary.simpleMessage("pace"),
    "paste": MessageLookupByLibrary.simpleMessage("Paste"),
    "paste_url_here": MessageLookupByLibrary.simpleMessage("Paste URL here"),
    "paste_youtube_link": MessageLookupByLibrary.simpleMessage(
      "Paste a Youtube link",
    ),
    "payment_required": MessageLookupByLibrary.simpleMessage(
      "You have used up your free credits",
    ),
    "payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Payment Success",
    ),
    "pdf_export": MessageLookupByLibrary.simpleMessage("PDF Export"),
    "pdf_pdf": MessageLookupByLibrary.simpleMessage("PDF (.pdf)"),
    "per_year": MessageLookupByLibrary.simpleMessage(" per year"),
    "period": MessageLookupByLibrary.simpleMessage(" of %s mins"),
    "personalized_learning": MessageLookupByLibrary.simpleMessage(
      "Practice your way to",
    ),
    "personalized_learning_at": MessageLookupByLibrary.simpleMessage(
      "Practice your way to an A+",
    ),
    "photos": MessageLookupByLibrary.simpleMessage("Photos"),
    "pick_specific_language": MessageLookupByLibrary.simpleMessage(
      "Pick a specific language in the audio for better transcribing accuracy",
    ),
    "plan": MessageLookupByLibrary.simpleMessage("Plan"),
    "please_select_a_language": MessageLookupByLibrary.simpleMessage(
      "Please select a language first so we can transcribe the audio accurately!",
    ),
    "please_select_a_youtube_language": MessageLookupByLibrary.simpleMessage(
      "Please select a summary language. This is the language you will see in the summary output",
    ),
    "please_try_again": MessageLookupByLibrary.simpleMessage(
      "Please try again",
    ),
    "please_wait": MessageLookupByLibrary.simpleMessage("Please wait"),
    "podcast": MessageLookupByLibrary.simpleMessage("Podcast"),
    "podcast_name": MessageLookupByLibrary.simpleMessage("Podcast Name"),
    "policy": MessageLookupByLibrary.simpleMessage("Policy"),
    "premium_features": MessageLookupByLibrary.simpleMessage(
      "Try premium features to see the difference",
    ),
    "preparing_video": MessageLookupByLibrary.simpleMessage(
      "Preparing video...",
    ),
    "press_back_again_to_exit": MessageLookupByLibrary.simpleMessage(
      "Press Back again to exit!",
    ),
    "preview_only": MessageLookupByLibrary.simpleMessage(
      "Preview only. Background will be AI-generated based on content",
    ),
    "priority_processing": MessageLookupByLibrary.simpleMessage(
      "Priority processing",
    ),
    "privacy_policy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "private": MessageLookupByLibrary.simpleMessage("Private"),
    "pro": MessageLookupByLibrary.simpleMessage("PRO Plan"),
    "proLite": MessageLookupByLibrary.simpleMessage("Pro Lite"),
    "pro_01": MessageLookupByLibrary.simpleMessage("Pro"),
    "pro_6_hours": MessageLookupByLibrary.simpleMessage(
      "Pro: 6 hours per file",
    ),
    "pro_access_life_time": MessageLookupByLibrary.simpleMessage(
      "PRO ACCESS LIFETIME",
    ),
    "pro_lifetime": MessageLookupByLibrary.simpleMessage("PRO Lifetime"),
    "process_your_document": MessageLookupByLibrary.simpleMessage(
      "Processing your document...",
    ),
    "process_your_text": MessageLookupByLibrary.simpleMessage(
      "Processing your text...",
    ),
    "processing_content": MessageLookupByLibrary.simpleMessage(
      "Processing content...",
    ),
    "processing_file": MessageLookupByLibrary.simpleMessage(
      "Processing file..",
    ),
    "processing_image": MessageLookupByLibrary.simpleMessage(
      "Processing image...",
    ),
    "processing_note_audio_file": MessageLookupByLibrary.simpleMessage(
      "Processing Your Audio...",
    ),
    "processing_note_recording": MessageLookupByLibrary.simpleMessage(
      "Processing Your Recording...",
    ),
    "processing_note_youtube": MessageLookupByLibrary.simpleMessage(
      "Processing YouTube Video...",
    ),
    "processing_web_link": MessageLookupByLibrary.simpleMessage(
      "Processing web link",
    ),
    "producing_flashcards": MessageLookupByLibrary.simpleMessage(
      "Producing AI Flashcards..",
    ),
    "professional": MessageLookupByLibrary.simpleMessage("Business Use"),
    "professional_description": MessageLookupByLibrary.simpleMessage(
      "Formal language suitable for work context",
    ),
    "professional_style": MessageLookupByLibrary.simpleMessage("Professional"),
    "public": MessageLookupByLibrary.simpleMessage("Public"),
    "purchase_fail": MessageLookupByLibrary.simpleMessage(
      "Purchase fail! Please try again!",
    ),
    "purchase_init_fail": MessageLookupByLibrary.simpleMessage(
      "Oops! We couldn\'t start your purchase. Please try again.",
    ),
    "purpose_using": MessageLookupByLibrary.simpleMessage("purpose for using "),
    "quantity": MessageLookupByLibrary.simpleMessage("Quantity"),
    "quarter": MessageLookupByLibrary.simpleMessage("quarter"),
    "quarterly": MessageLookupByLibrary.simpleMessage("Quarterly"),
    "question": MessageLookupByLibrary.simpleMessage("Question"),
    "quick_access": MessageLookupByLibrary.simpleMessage("Quick Access"),
    "quick_import": MessageLookupByLibrary.simpleMessage(
      "Or quickly import from",
    ),
    "quickly": MessageLookupByLibrary.simpleMessage("concepts"),
    "quiz": MessageLookupByLibrary.simpleMessage("Quiz"),
    "quiz_count": MessageLookupByLibrary.simpleMessage("Quiz Count"),
    "quiz_diff": MessageLookupByLibrary.simpleMessage("Quiz Difficulty"),
    "quiz_gen_success": MessageLookupByLibrary.simpleMessage(
      "Quiz generated success",
    ),
    "quiz_iap": MessageLookupByLibrary.simpleMessage("Quiz Set"),
    "quiz_master": MessageLookupByLibrary.simpleMessage("AI Quiz Master"),
    "quiz_score": MessageLookupByLibrary.simpleMessage("Quiz score"),
    "quiz_set": MessageLookupByLibrary.simpleMessage("Quiz Sets"),
    "quiz_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Quiz set not found",
    ),
    "quizz_for": MessageLookupByLibrary.simpleMessage("Quizz for"),
    "quizzes": MessageLookupByLibrary.simpleMessage("Quizzes"),
    "rate": MessageLookupByLibrary.simpleMessage("Rate"),
    "rate_five_stars": MessageLookupByLibrary.simpleMessage("Rate us 5 stars"),
    "rate_us_on_store": MessageLookupByLibrary.simpleMessage(
      "Rate Us on Store",
    ),
    "rating_cmt1": MessageLookupByLibrary.simpleMessage(
      "This app is truly amazing, and it\'s only getting better. Thanks to the developers for their hard work and dedication. I recommend it over any other AI note-taking app on the App Store.",
    ),
    "rating_cmt2": MessageLookupByLibrary.simpleMessage(
      "Absolutely love this app! The perfect companion for all my meetings.",
    ),
    "rating_cmt3": MessageLookupByLibrary.simpleMessage(
      "Cut my study time in half. More time for coffee breaks!",
    ),
    "rating_cmt4": MessageLookupByLibrary.simpleMessage(
      "This app is absolutely mind-blowing! Not only does it nail transcription, but it takes things to another level with incredible summaries, outlines, and action items. Pure genius!",
    ),
    "rating_cmt5": MessageLookupByLibrary.simpleMessage(
      "Wonderful, powerful! Everything you want and more.Everything you want and more.",
    ),
    "rating_cmt6": MessageLookupByLibrary.simpleMessage(
      "I tried this for the first time today by entering a YouTube presentation. Within seconds it gave me a complete transcription, a well-designed brain chart, and a series of flash cards on the subject. This was far beyond anything I had anticipated. It is efficient, sophisticated, and wonderfully useful. This is the app I will be using daily for notes and learning.",
    ),
    "rating_cmt7": MessageLookupByLibrary.simpleMessage(
      "So far this is the best note app I\'ve found. It has lots of useful features.",
    ),
    "rating_cmt8": MessageLookupByLibrary.simpleMessage(
      "Captures every detail from marathon biology lectures. Summary feature is a lifesaver during exam prep.",
    ),
    "rating_sub_context_1": MessageLookupByLibrary.simpleMessage(
      "Mind Blowing!",
    ),
    "rating_sub_context_2": MessageLookupByLibrary.simpleMessage("Meeting Pro"),
    "rating_sub_context_3": MessageLookupByLibrary.simpleMessage("Time Saver"),
    "rating_sub_context_4": MessageLookupByLibrary.simpleMessage(
      "AI Note Taking It Is Best",
    ),
    "rating_sub_context_5": MessageLookupByLibrary.simpleMessage(
      "Like This App So Much",
    ),
    "rating_sub_context_6": MessageLookupByLibrary.simpleMessage(
      "AI Note Taking It Is Best",
    ),
    "rating_sub_context_7": MessageLookupByLibrary.simpleMessage(
      "Best Note Taking I\'ve Used",
    ),
    "rating_sub_context_8": MessageLookupByLibrary.simpleMessage(
      "So Far The Best",
    ),
    "record": MessageLookupByLibrary.simpleMessage("Recording"),
    "record_audio": MessageLookupByLibrary.simpleMessage("Record Audio"),
    "record_audio_coming_soon": MessageLookupByLibrary.simpleMessage(
      "Record Audio (coming soon)",
    ),
    "record_over_x_min": MessageLookupByLibrary.simpleMessage(
      "Recording Over %s Minutes",
    ),
    "record_over_x_min_details": MessageLookupByLibrary.simpleMessage(
      "Your recordings will be saved locally without AI transcriptions and summarization. You can remove all limits to process this recording after it\'s complete.",
    ),
    "record_summarize_lecture": MessageLookupByLibrary.simpleMessage(
      "Record & Summarize College Lectures",
    ),
    "recording": MessageLookupByLibrary.simpleMessage("Recording"),
    "recording_in_progress": MessageLookupByLibrary.simpleMessage(
      "Recording in progress",
    ),
    "recording_in_progress_content": MessageLookupByLibrary.simpleMessage(
      "Recording...",
    ),
    "recording_paused": MessageLookupByLibrary.simpleMessage(
      "Recording paused",
    ),
    "recording_paused_content": MessageLookupByLibrary.simpleMessage(
      "Press to resume",
    ),
    "recording_permission_denied": MessageLookupByLibrary.simpleMessage(
      "Recording Permission is Denied!",
    ),
    "recording_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "Go to Setting to allow",
    ),
    "recording_quality": MessageLookupByLibrary.simpleMessage(
      "Recording quality",
    ),
    "recording_schedule": MessageLookupByLibrary.simpleMessage(
      "Recording Schedule",
    ),
    "recording_voice_note": MessageLookupByLibrary.simpleMessage(
      "Recording Voice Note",
    ),
    "redeem_7_days_for_0": MessageLookupByLibrary.simpleMessage(
      "Redeem 7 days for 0",
    ),
    "redeem_credits": MessageLookupByLibrary.simpleMessage("Redeem"),
    "refer_now": MessageLookupByLibrary.simpleMessage("Refer Now"),
    "refer_rewards": MessageLookupByLibrary.simpleMessage("Refer & Rewards"),
    "referral": MessageLookupByLibrary.simpleMessage("Referral"),
    "referral_02": MessageLookupByLibrary.simpleMessage("REFERRAL"),
    "referral_already_used": MessageLookupByLibrary.simpleMessage(
      "Referral code has already been used.",
    ),
    "referral_code": MessageLookupByLibrary.simpleMessage("Referral code"),
    "referral_credits": MessageLookupByLibrary.simpleMessage(
      "Referral Credits",
    ),
    "referral_not_found": MessageLookupByLibrary.simpleMessage(
      "Referral code not found.",
    ),
    "referral_self_use": MessageLookupByLibrary.simpleMessage(
      "You cannot use your own referral code.",
    ),
    "referral_time_expired": MessageLookupByLibrary.simpleMessage(
      "Referral code has expired after 24 hours.",
    ),
    "referral_validation_err": MessageLookupByLibrary.simpleMessage(
      "Referral validation error.",
    ),
    "reload_tap": MessageLookupByLibrary.simpleMessage("Error, tap to reload"),
    "remain_recording_length": MessageLookupByLibrary.simpleMessage(
      "30s - 5min remaining based on recording length...",
    ),
    "reminders_record_audio": MessageLookupByLibrary.simpleMessage(
      "Set up weekly audio recording times",
    ),
    "remove_all_limits": MessageLookupByLibrary.simpleMessage(
      "Remove All Limits",
    ),
    "replace": MessageLookupByLibrary.simpleMessage("Replace"),
    "replace_all": MessageLookupByLibrary.simpleMessage("Replace All"),
    "report_issue": MessageLookupByLibrary.simpleMessage(
      "How to Report an Issue?",
    ),
    "report_issue2": MessageLookupByLibrary.simpleMessage(
      "We\'re Here to Help:",
    ),
    "required": MessageLookupByLibrary.simpleMessage("Ex: Folder_name A"),
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "restart_now": MessageLookupByLibrary.simpleMessage("Restart now"),
    "restore": MessageLookupByLibrary.simpleMessage("Restore"),
    "restore_fail_message": MessageLookupByLibrary.simpleMessage(
      "For assistance, <NAME_EMAIL>",
    ),
    "restore_fail_title": MessageLookupByLibrary.simpleMessage(
      "No items available for restoration",
    ),
    "restore_purchase": MessageLookupByLibrary.simpleMessage(
      "Restore purchase",
    ),
    "restore_success_title": MessageLookupByLibrary.simpleMessage(
      "Restore success",
    ),
    "retention": MessageLookupByLibrary.simpleMessage("and retention"),
    "retention_quickly": MessageLookupByLibrary.simpleMessage(
      "between concepts",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "sale_off": MessageLookupByLibrary.simpleMessage("SALE OFF"),
    "satisfied": MessageLookupByLibrary.simpleMessage(
      "Thank you for your feedback!",
    ),
    "satisfied_quality": MessageLookupByLibrary.simpleMessage(
      "Is this note clear and useful?",
    ),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "save_50": MessageLookupByLibrary.simpleMessage("Save 50%"),
    "save_changes": MessageLookupByLibrary.simpleMessage("Save Changes?"),
    "save_chat": MessageLookupByLibrary.simpleMessage("Save Chat"),
    "save_file": MessageLookupByLibrary.simpleMessage("File has been saved"),
    "saved_chat": MessageLookupByLibrary.simpleMessage("Saved Chat"),
    "saved_successfully": MessageLookupByLibrary.simpleMessage(
      "Saved Successfully",
    ),
    "saving_recording": MessageLookupByLibrary.simpleMessage(
      "Saving recording to device",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "search_emoji": MessageLookupByLibrary.simpleMessage("Search Emoji"),
    "search_in_files": MessageLookupByLibrary.simpleMessage("Search in Files"),
    "searching_all_notes": MessageLookupByLibrary.simpleMessage(
      "Searching all notes",
    ),
    "seconds": MessageLookupByLibrary.simpleMessage("Seconds"),
    "select": MessageLookupByLibrary.simpleMessage("Select"),
    "select_a_language": MessageLookupByLibrary.simpleMessage(
      "Select a language to use throughout the recording process before saving.",
    ),
    "select_all": MessageLookupByLibrary.simpleMessage("Select All"),
    "select_and_reorder": MessageLookupByLibrary.simpleMessage(
      "Select and reorder your note modules. A minimum of 4 tabs is required to continue.",
    ),
    "select_language": MessageLookupByLibrary.simpleMessage("Select Language"),
    "select_your_note": MessageLookupByLibrary.simpleMessage(
      "Select your note modules.",
    ),
    "select_your_primary_use_case": MessageLookupByLibrary.simpleMessage(
      "Select your primary use case",
    ),
    "server_err": MessageLookupByLibrary.simpleMessage(
      "An unknown server error occurred.",
    ),
    "server_error": MessageLookupByLibrary.simpleMessage(
      "Something went wrong",
    ),
    "setting": MessageLookupByLibrary.simpleMessage("Settings"),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "seven_day_free": MessageLookupByLibrary.simpleMessage("7 days free, then"),
    "share": MessageLookupByLibrary.simpleMessage("Send to Friends"),
    "share_audio_file": MessageLookupByLibrary.simpleMessage("Share audio"),
    "share_code_friends": MessageLookupByLibrary.simpleMessage(
      "Share the code with friends via email, social media, or messages.",
    ),
    "share_file": MessageLookupByLibrary.simpleMessage("Share Audio File"),
    "share_note": MessageLookupByLibrary.simpleMessage("Share Notes"),
    "share_note_link": MessageLookupByLibrary.simpleMessage("Share Note"),
    "share_only": MessageLookupByLibrary.simpleMessage("Share"),
    "share_referral_code_start_earning_credits":
        MessageLookupByLibrary.simpleMessage(
          "Share your referral code to start earning credits!",
        ),
    "share_summary": MessageLookupByLibrary.simpleMessage("Copy Summary"),
    "share_sync": MessageLookupByLibrary.simpleMessage("Share & Sync"),
    "share_transcript": MessageLookupByLibrary.simpleMessage("Copy Transcript"),
    "share_with_link": MessageLookupByLibrary.simpleMessage("Share with link:"),
    "shared": MessageLookupByLibrary.simpleMessage("Shared"),
    "sharing_export": MessageLookupByLibrary.simpleMessage("Sharing & Export"),
    "short": MessageLookupByLibrary.simpleMessage("Short"),
    "short_description": MessageLookupByLibrary.simpleMessage(
      "Key points only",
    ),
    "shorts": MessageLookupByLibrary.simpleMessage("Shorts"),
    "show_your_love": MessageLookupByLibrary.simpleMessage(
      "Show your love by giving us a",
    ),
    "signing_in": MessageLookupByLibrary.simpleMessage("Signing in..."),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "slide_count": MessageLookupByLibrary.simpleMessage("Slide Count"),
    "slide_count_tooltip": MessageLookupByLibrary.simpleMessage(
      "Maximum of 12 Slides per Template",
    ),
    "slide_range": MessageLookupByLibrary.simpleMessage("Slide Range"),
    "slide_show": MessageLookupByLibrary.simpleMessage("Slideshow"),
    "smart_learning": MessageLookupByLibrary.simpleMessage("Smart Learning"),
    "smart_note_big_ideas": MessageLookupByLibrary.simpleMessage(
      "Smart Notes, Big Ideas",
    ),
    "smart_quizzes": MessageLookupByLibrary.simpleMessage(
      "Unlimited adaptive Quizzes",
    ),
    "smart_start": MessageLookupByLibrary.simpleMessage("Smart starter pack"),
    "sort_by": MessageLookupByLibrary.simpleMessage("Sort by"),
    "special_gift": MessageLookupByLibrary.simpleMessage("Special Gift"),
    "special_gift_title": MessageLookupByLibrary.simpleMessage("SPECIAL GIFT"),
    "special_offer": MessageLookupByLibrary.simpleMessage("SPECIAL\nOFFER"),
    "speech_language": MessageLookupByLibrary.simpleMessage("Speech Language"),
    "start_for_free": MessageLookupByLibrary.simpleMessage("Start For Free"),
    "start_free_trial": MessageLookupByLibrary.simpleMessage(
      "Start free trial",
    ),
    "start_my_7_day_trial": MessageLookupByLibrary.simpleMessage(
      "Start my 7-Day Trial",
    ),
    "start_record": MessageLookupByLibrary.simpleMessage("Start Record"),
    "start_speaking": MessageLookupByLibrary.simpleMessage("Start Speaking"),
    "step1": MessageLookupByLibrary.simpleMessage(
      "In the NoteX app, go to Settings.",
    ),
    "step2": MessageLookupByLibrary.simpleMessage(
      "Locate the app version at the bottom (e.g., v1.4.0(6)).",
    ),
    "step3": MessageLookupByLibrary.simpleMessage(
      "Tap the app version 5 times quickly.",
    ),
    "step4": MessageLookupByLibrary.simpleMessage(
      "Your unique userID will be automatically copied to your clipboard.",
    ),
    "step5": MessageLookupByLibrary.simpleMessage(
      "In your message below, please include:",
    ),
    "step51": MessageLookupByLibrary.simpleMessage(
      "Your userID (paste it from your clipboard).",
    ),
    "step52": MessageLookupByLibrary.simpleMessage(
      "A brief description of the issue you\'re experiencing.",
    ),
    "step53": MessageLookupByLibrary.simpleMessage(
      "Any relevant details (e.g., device model, iOS version).",
    ),
    "step6": MessageLookupByLibrary.simpleMessage("Send us an email at "),
    "student": MessageLookupByLibrary.simpleMessage("Academic Use"),
    "style": MessageLookupByLibrary.simpleMessage("Style"),
    "sub_rip": MessageLookupByLibrary.simpleMessage("SubRip (.srt)"),
    "sub_user_have_unlimited": MessageLookupByLibrary.simpleMessage(
      "Subscribed users have unlimited use and access to all premium features without ads Non-subscribed users can continuously use the app with advertisements and have limited use of premium features Payment will be charged to Google Play account at confirmation of purchases. The subscription will be auto-renewed unless it is turned off within 24 hours before the end of the period. Your account will be charged according to your plan for renewal within 24 hours to the end of the current period. Any unused portion of a free trial period, if offered, will be forfeited when user purchases a subscription where applicable. You can manage or turn off auto-renew at the subscription page of Google Play after purchase. Note that uninstalling the app will not cancel your subscription.",
    ),
    "sub_will_auto_renew": MessageLookupByLibrary.simpleMessage(
      "Subscriptions will be auto renew. Cancel anytime.",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "submit_button": MessageLookupByLibrary.simpleMessage("Submit"),
    "subscribe": MessageLookupByLibrary.simpleMessage("Subscribe"),
    "subscribe_via_web": MessageLookupByLibrary.simpleMessage(
      "If you subscribed via web, manage at notexapp.com/setting",
    ),
    "success": MessageLookupByLibrary.simpleMessage("Success"),
    "successfully": MessageLookupByLibrary.simpleMessage("Successfully"),
    "suggest_features": MessageLookupByLibrary.simpleMessage(
      "Suggest Features",
    ),
    "suggested": MessageLookupByLibrary.simpleMessage("Suggested"),
    "summarize_video": MessageLookupByLibrary.simpleMessage(
      "Summarize Long YouTube Videos",
    ),
    "summary": MessageLookupByLibrary.simpleMessage("Summary"),
    "summary_language": MessageLookupByLibrary.simpleMessage(
      "Summary Language",
    ),
    "summary_style": MessageLookupByLibrary.simpleMessage("Summary Style"),
    "summary_successful": MessageLookupByLibrary.simpleMessage(
      "Summary is successfully created!",
    ),
    "summary_usefulness": MessageLookupByLibrary.simpleMessage(
      "Summary usefulness",
    ),
    "supercharge": MessageLookupByLibrary.simpleMessage(
      "Achieve More, Stress Less",
    ),
    "support_audio": MessageLookupByLibrary.simpleMessage(
      "Supported file types: .mp3, .wav, .ogg, .m4a",
    ),
    "support_for_up_to_10_images": MessageLookupByLibrary.simpleMessage(
      "Support for up to 10 images",
    ),
    "support_image": MessageLookupByLibrary.simpleMessage(
      "Supported image types: .png, .jpg, .heif, .heic",
    ),
    "support_over_onehundred_languages": MessageLookupByLibrary.simpleMessage(
      "100+ Languages supported",
    ),
    "support_youtube_and_more": MessageLookupByLibrary.simpleMessage(
      "Supports YouTube, Web, TikTok, Instagram, Facebook & more",
    ),
    "switch_mode": MessageLookupByLibrary.simpleMessage("Switch Mode"),
    "sync_from_watch": MessageLookupByLibrary.simpleMessage("Sync from Watch"),
    "sync_notes": MessageLookupByLibrary.simpleMessage(
      "Sync notes in computer browser",
    ),
    "system": MessageLookupByLibrary.simpleMessage("System"),
    "tap_cancel": MessageLookupByLibrary.simpleMessage(
      "Tap Cancel subscription",
    ),
    "tap_menu": MessageLookupByLibrary.simpleMessage(
      "Tap Menu Subscriptions and select the subscription you want to cancel",
    ),
    "tap_the": MessageLookupByLibrary.simpleMessage("Tap the"),
    "tap_the_record": MessageLookupByLibrary.simpleMessage("Tap the Record"),
    "tap_to_record": MessageLookupByLibrary.simpleMessage(
      "Tap to record your thoughts",
    ),
    "task_create_err": MessageLookupByLibrary.simpleMessage(
      "Error creating task. Please try again later.",
    ),
    "templates": MessageLookupByLibrary.simpleMessage("Templates"),
    "term_and_cond": MessageLookupByLibrary.simpleMessage(
      "Terms and Conditions",
    ),
    "terms": MessageLookupByLibrary.simpleMessage("Terms"),
    "terms_of_sub": MessageLookupByLibrary.simpleMessage(
      "Terms of Subscriptions",
    ),
    "terms_of_use": MessageLookupByLibrary.simpleMessage("Terms of Use"),
    "text": MessageLookupByLibrary.simpleMessage("Add Text"),
    "text_must_not_exceed_50_chars": MessageLookupByLibrary.simpleMessage(
      "The text contains only 50 characters",
    ),
    "thank_feedback": MessageLookupByLibrary.simpleMessage(
      "Thank for Feedback!",
    ),
    "thinking": MessageLookupByLibrary.simpleMessage("Thinking..."),
    "thirty_min_per": MessageLookupByLibrary.simpleMessage(
      "30 min per \n week",
    ),
    "this_folder_empty": MessageLookupByLibrary.simpleMessage(
      "Time to drop your first AI note!  ✨",
    ),
    "this_free_trial": MessageLookupByLibrary.simpleMessage(
      "This free trial is an introductory offer for new users only. Experience all pro features for the whole week before deciding.",
    ),
    "this_is_the_language": MessageLookupByLibrary.simpleMessage(
      "This is the language you will see in the summary output",
    ),
    "thousands_trusted": MessageLookupByLibrary.simpleMessage(
      "Rated 4.8/5: Trusted by thousands",
    ),
    "time": MessageLookupByLibrary.simpleMessage("Time"),
    "time_black_friday": MessageLookupByLibrary.simpleMessage("22 - 30 Nov"),
    "time_black_friday_2": MessageLookupByLibrary.simpleMessage(
      "22 - 30 November",
    ),
    "time_out": MessageLookupByLibrary.simpleMessage(
      "Request timeout. Please try again.",
    ),
    "title": MessageLookupByLibrary.simpleMessage("Title"),
    "title_error_note": MessageLookupByLibrary.simpleMessage(
      "Note Creation Unsuccessful",
    ),
    "title_success_note": MessageLookupByLibrary.simpleMessage(
      "AI Notes Successfully Created",
    ),
    "to": MessageLookupByLibrary.simpleMessage("to"),
    "to_day": MessageLookupByLibrary.simpleMessage("Today"),
    "token_expired": MessageLookupByLibrary.simpleMessage("Token has expired!"),
    "tolower_credits": MessageLookupByLibrary.simpleMessage("credits"),
    "tool_tip_language": MessageLookupByLibrary.simpleMessage(
      "Select the main speech language before saving this recording",
    ),
    "topic_option": MessageLookupByLibrary.simpleMessage("Topic (optional)"),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "transcribing": MessageLookupByLibrary.simpleMessage(
      "Transcribing with best AI",
    ),
    "transcribing_audio": MessageLookupByLibrary.simpleMessage(
      "Transcribing audio..",
    ),
    "transcript": MessageLookupByLibrary.simpleMessage("Transcript"),
    "transcript_context": MessageLookupByLibrary.simpleMessage(
      "Transcript Context",
    ),
    "transcript_language": MessageLookupByLibrary.simpleMessage(
      "Transcript Language",
    ),
    "transcript_line_cannot_be_empty": MessageLookupByLibrary.simpleMessage(
      "Transcript line cannot be empty",
    ),
    "transcript_line_tool_tip": MessageLookupByLibrary.simpleMessage(
      "Click on the transcript item to edit",
    ),
    "transcription_precision": MessageLookupByLibrary.simpleMessage(
      "Transcription precision",
    ),
    "transform_meetings": MessageLookupByLibrary.simpleMessage(
      "Transform meetings into",
    ),
    "transform_meetings_into_actionable_intelligence":
        MessageLookupByLibrary.simpleMessage(
          "Transform meetings into actionable intelligence",
        ),
    "translate_note": MessageLookupByLibrary.simpleMessage("Translate Note"),
    "translating_note": MessageLookupByLibrary.simpleMessage(
      "Translating Note...",
    ),
    "translation_completed": MessageLookupByLibrary.simpleMessage(
      "Translation Completed",
    ),
    "translation_failed": MessageLookupByLibrary.simpleMessage(
      "Translation Failed",
    ),
    "trouble_connecting_to_server": MessageLookupByLibrary.simpleMessage(
      "We\'re having some trouble connecting to the server. Please try again in a moment.",
    ),
    "try_3_day": MessageLookupByLibrary.simpleMessage("Try 3 Days Free"),
    "try_7_day": MessageLookupByLibrary.simpleMessage("Start My 7-Day Trial"),
    "try_again": MessageLookupByLibrary.simpleMessage(
      "We encountered a slight hiccup while generating your notes. Please try again!",
    ),
    "try_again_button": MessageLookupByLibrary.simpleMessage("Try Again"),
    "try_pro_free_7_day": MessageLookupByLibrary.simpleMessage(
      "Try Pro Free for 7 Days",
    ),
    "type_or_paste_any_text_here": MessageLookupByLibrary.simpleMessage(
      "Type or paste any text here. AI will transform it into a clear, structured summary with key highlights.",
    ),
    "uidCopied": m3,
    "unable_download_file": MessageLookupByLibrary.simpleMessage(
      "Unable to download file",
    ),
    "unable_load_audio": MessageLookupByLibrary.simpleMessage(
      "Unable to load audio:",
    ),
    "unable_share_audio": MessageLookupByLibrary.simpleMessage(
      "Unable to share audio file",
    ),
    "unable_to_connect_to_server": MessageLookupByLibrary.simpleMessage(
      "Make sure your phone are connected to the internet",
    ),
    "unable_to_extract_web_url": MessageLookupByLibrary.simpleMessage(
      "Unable to extract content from web URL",
    ),
    "unable_to_open_store": MessageLookupByLibrary.simpleMessage(
      "Unable to open store",
    ),
    "uncover_opportunities": MessageLookupByLibrary.simpleMessage(
      "uncover opportunities",
    ),
    "unknown_error": MessageLookupByLibrary.simpleMessage(
      "The application has encountered an unknown error",
    ),
    "unknown_server_error": MessageLookupByLibrary.simpleMessage(
      "Oops! Our servers stumbled. Please try again.",
    ),
    "unlimited_ai_chat": MessageLookupByLibrary.simpleMessage(
      "Unlimited AI Chat, AI Mind Map, Flashcard, Quiz",
    ),
    "unlimited_ai_chat_ai_mind_map_flashcard_quiz":
        MessageLookupByLibrary.simpleMessage(
          "Unlimited AI Chat, AI Mind Map, Flashcard, Quiz",
        ),
    "unlimited_ai_note": MessageLookupByLibrary.simpleMessage(
      "Unlimited AI note generation from all sources (YouTube, Documents, Recording, Audio)",
    ),
    "unlimited_ai_notes_from_youtube_and_document":
        MessageLookupByLibrary.simpleMessage(
          "Unlimited AI notes from YouTube & Document",
        ),
    "unlimited_audio_youtube_website_to_ai_notes":
        MessageLookupByLibrary.simpleMessage(
          "Unlimited Audio, YouTube , Document & Website to AI Notes",
        ),
    "unlimited_everything": MessageLookupByLibrary.simpleMessage(
      "Experience unlimited AI notes, priority service, and premium features",
    ),
    "unlimited_youtube_document_ai_notes": MessageLookupByLibrary.simpleMessage(
      "Unlimited YouTube & Document AI Notes",
    ),
    "unlock_all_features": MessageLookupByLibrary.simpleMessage(
      "Unlock All Features",
    ),
    "unlock_essential_life_time": MessageLookupByLibrary.simpleMessage(
      "Unlock Essential Lifetime",
    ),
    "unlock_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Unlock Lifetime Access",
    ),
    "unlock_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "Unlock PRO Lifetime",
    ),
    "unlock_the_most_ipad": MessageLookupByLibrary.simpleMessage(
      "Unlock the most powerful AI note-taking assistant",
    ),
    "unlock_the_most_powerful_ai_note_taking_assistant":
        MessageLookupByLibrary.simpleMessage(
          "Unlock the most powerful AI \nnote-taking assistant",
        ),
    "unlock_toge": MessageLookupByLibrary.simpleMessage("UNLOCK TOGETHER"),
    "unlock_together": MessageLookupByLibrary.simpleMessage("Unlock Together"),
    "unlock_unlimited_access_to_all_ai_features":
        MessageLookupByLibrary.simpleMessage(
          "Unlock unlimited access to all AI features",
        ),
    "unlock_unlimited_ai": MessageLookupByLibrary.simpleMessage(
      "Unlock unlimited AI experience",
    ),
    "unsynced_notes": MessageLookupByLibrary.simpleMessage("Unsynced Notes"),
    "update_available": MessageLookupByLibrary.simpleMessage(
      "A new update is available! Please update to the latest version to enjoy the best experience.",
    ),
    "update_failed": MessageLookupByLibrary.simpleMessage(
      "Failed to update community notes. Please try again.",
    ),
    "update_later": MessageLookupByLibrary.simpleMessage("Later"),
    "update_now": MessageLookupByLibrary.simpleMessage("Update Now!"),
    "update_pro": MessageLookupByLibrary.simpleMessage(
      "Upgrade to Pro Experience",
    ),
    "update_to_pro": MessageLookupByLibrary.simpleMessage("Update to PRO"),
    "upgrade": MessageLookupByLibrary.simpleMessage("UPGRADE"),
    "upgrade_now": MessageLookupByLibrary.simpleMessage("Upgrade Now!"),
    "upgrade_plan": MessageLookupByLibrary.simpleMessage("Upgrade Plan"),
    "upgrade_pro_2": MessageLookupByLibrary.simpleMessage("UPGRADE PRO"),
    "upgrade_to_full_pro_access": MessageLookupByLibrary.simpleMessage(
      "Upgrade to Full Pro Access",
    ),
    "upgrade_to_pro_tier_at_a_special_price":
        MessageLookupByLibrary.simpleMessage(
          "Upgrade to Pro Tier at a Special Price",
        ),
    "upload": MessageLookupByLibrary.simpleMessage("Upload"),
    "upload_audio": MessageLookupByLibrary.simpleMessage("Upload Audio"),
    "upload_audio_file": MessageLookupByLibrary.simpleMessage(
      "Upload audio file",
    ),
    "upload_file": MessageLookupByLibrary.simpleMessage("Upload File"),
    "upload_image": MessageLookupByLibrary.simpleMessage("Upload Image"),
    "upload_in_progress": MessageLookupByLibrary.simpleMessage(
      "Upload in progress. Keep screen open.\n Disable VPN for faster upload.",
    ),
    "uploading_to_server": MessageLookupByLibrary.simpleMessage(
      "Uploading to secure server",
    ),
    "user_disabled": MessageLookupByLibrary.simpleMessage(
      "The user corresponding to this email has been disabled.",
    ),
    "user_not_found": MessageLookupByLibrary.simpleMessage(
      "User information not found.",
    ),
    "verifying_your_credentials": MessageLookupByLibrary.simpleMessage(
      "Verifying your credentials",
    ),
    "video": MessageLookupByLibrary.simpleMessage("Video"),
    "video_audio": MessageLookupByLibrary.simpleMessage(
      "Recordings, Videos & Docs to AI Notes",
    ),
    "video_captions": MessageLookupByLibrary.simpleMessage("Video Captions"),
    "video_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*Video is temporary - Save before closing",
    ),
    "visualize_strategies": MessageLookupByLibrary.simpleMessage(
      "Visualize strategies and",
    ),
    "visualize_strategies_opportunities": MessageLookupByLibrary.simpleMessage(
      "Visualize strategies and uncover opportunities",
    ),
    "visualize_strategies_uncover": MessageLookupByLibrary.simpleMessage(
      "Visualize strategies and uncover",
    ),
    "visualize_strategies_uncover_opportunities":
        MessageLookupByLibrary.simpleMessage(
          "Visualize strategies and uncover opportunities",
        ),
    "voice": MessageLookupByLibrary.simpleMessage("Voice"),
    "warning_this_ai_note_taking_app_may_cause_excessive_productivity":
        MessageLookupByLibrary.simpleMessage(
          "Warning: This AI note-taking app may cause excessive productivity! 🚀 Use my code and we\'ll both get extra usage. Code: ",
        ),
    "watch_sync_empty_message": MessageLookupByLibrary.simpleMessage(
      "Recordings from your Apple Watch will appear here",
    ),
    "web": MessageLookupByLibrary.simpleMessage("Web"),
    "web_link": MessageLookupByLibrary.simpleMessage("Web Link"),
    "web_sync": MessageLookupByLibrary.simpleMessage("Web Sync"),
    "website_import": MessageLookupByLibrary.simpleMessage("Website Import"),
    "week": MessageLookupByLibrary.simpleMessage("week"),
    "week_free_limit": MessageLookupByLibrary.simpleMessage(
      "Weekly Free Limit Reached",
    ),
    "weekly": MessageLookupByLibrary.simpleMessage("Weekly"),
    "weekly_free_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Remove All Limits",
    ),
    "weekly_free_limit_reached_details": MessageLookupByLibrary.simpleMessage(
      "You\'ve used all free transcriptions and AI summaries for this week! Upgrade to Pro for unlimited access or wait until next week for your free quota to reset.",
    ),
    "welcome_notex": MessageLookupByLibrary.simpleMessage("Welcome to NoteX!"),
    "welcome_title": MessageLookupByLibrary.simpleMessage(
      "Let\'s create your\nfirst AI note",
    ),
    "what_improve": MessageLookupByLibrary.simpleMessage(
      "What you need to be improved",
    ),
    "whats_new": MessageLookupByLibrary.simpleMessage("What\'s new"),
    "word_docx": MessageLookupByLibrary.simpleMessage("Word (.docx)"),
    "work_notes_projects": MessageLookupByLibrary.simpleMessage(
      "Work Notes & Projects",
    ),
    "writing_style": MessageLookupByLibrary.simpleMessage("Writing Style"),
    "wrong": MessageLookupByLibrary.simpleMessage("Wrong"),
    "x": MessageLookupByLibrary.simpleMessage("X"),
    "x_skip": MessageLookupByLibrary.simpleMessage("X?"),
    "year": MessageLookupByLibrary.simpleMessage("year"),
    "yearly": MessageLookupByLibrary.simpleMessage("Yearly"),
    "yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
    "you_are_given_a_special_gift_today": MessageLookupByLibrary.simpleMessage(
      "You are given a special gift today 🎁",
    ),
    "you_are_pro": MessageLookupByLibrary.simpleMessage("PRO Access"),
    "you_can_update_setting": MessageLookupByLibrary.simpleMessage(
      "You can update anytime in Settings.",
    ),
    "you_have_received": MessageLookupByLibrary.simpleMessage(
      "You have received",
    ),
    "you_have_received2": MessageLookupByLibrary.simpleMessage(
      "You\'ll get one entry to win NoteX Lifetime Pro Access! 3 lucky winners chosen on the 30th every month 🎁",
    ),
    "you_will_get_one_entry_to_win_noteX": MessageLookupByLibrary.simpleMessage(
      "Chance to win NoteX Lifetime Pro! 🚀",
    ),
    "you_will_not_be": MessageLookupByLibrary.simpleMessage(
      "You will not be able to recover them afterwards",
    ),
    "your_learning": MessageLookupByLibrary.simpleMessage(
      "Supercharge Your Learning!",
    ),
    "your_learning_device": MessageLookupByLibrary.simpleMessage(
      "NoteX Pro Access",
    ),
    "your_note_are_ready": MessageLookupByLibrary.simpleMessage(
      "Your notes are now ready.",
    ),
    "your_personal_study": MessageLookupByLibrary.simpleMessage(
      "Your personal study",
    ),
    "your_personal_study_assistant": MessageLookupByLibrary.simpleMessage(
      "Your personal study assistant",
    ),
    "your_plan": MessageLookupByLibrary.simpleMessage("Your Plan"),
    "your_primary": MessageLookupByLibrary.simpleMessage(
      "What is your primary",
    ),
    "your_product": MessageLookupByLibrary.simpleMessage("YOUR PRODUCTIVITY"),
    "your_recording_will_save": MessageLookupByLibrary.simpleMessage(
      "Your recordings will be saved locally without AI transcriptions and summarization. You can remove all limits to process this recording after it\'s complete.",
    ),
    "your_referrals": MessageLookupByLibrary.simpleMessage("Your Referrals"),
    "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
    "youtube_import": MessageLookupByLibrary.simpleMessage("YouTube Import"),
    "youtube_link": MessageLookupByLibrary.simpleMessage("Youtube Link"),
    "youtube_transcript_language_guidance": MessageLookupByLibrary.simpleMessage(
      "Select YouTube transcript language. This language will be used to generate your AI notes",
    ),
    "youtube_video": MessageLookupByLibrary.simpleMessage("Youtube Video"),
    "youtube_video_note": MessageLookupByLibrary.simpleMessage("YouTube Video"),
    "yt_credit_err": MessageLookupByLibrary.simpleMessage(
      "Insufficient YouTube free usage. Please upgrade your plan.",
    ),
    "yt_credit_use_err": MessageLookupByLibrary.simpleMessage(
      "Error using YouTube free usage. Please try again later.",
    ),
    "yt_length_err": MessageLookupByLibrary.simpleMessage(
      "YouTube video exceeds the 10-hour limit. Please choose a shorter video.",
    ),
    "yt_process_err": MessageLookupByLibrary.simpleMessage(
      "Error processing YouTube video. Please check the URL and try again.",
    ),
    "yt_sum_limit": MessageLookupByLibrary.simpleMessage(
      "YouTube Summary Limit",
    ),
    "z_to_a": MessageLookupByLibrary.simpleMessage("Z to A"),
  };
}
