import 'dart:developer' as developer;
import 'package:dio/dio.dart';
import 'package:note_x/lib.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class BaseApiService {
  final dio = g<Dio>(instanceName: Env.instance.baseURL);

  Future<void> ensureConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult.contains(ConnectivityResult.none)) {
      throw AppErrorException('No internet connection', 'no_internet');
    }
  }

  AppErrorException getError(dynamic exception) {
    try {
      if (exception is DioException) {
        if (exception.error is DioException) {
          final response = (exception.error as DioException).response;
          return AppErrorException.fromJson(response?.data);
        }

        // Handle 4xx client errors (including 400 Bad Request)
        final statusCode = exception.response?.statusCode;
        if (statusCode != null && statusCode >= 400 && statusCode < 500) {
          final responseData = exception.response?.data;
          if (responseData is Map<String, dynamic> &&
              responseData.containsKey('error_key')) {
            return AppErrorException.fromJson(responseData);
          }
        }

        //
        if (exception.type == DioExceptionType.connectionError) {
          return AppErrorException(
            'Connection Error',
            'no_internet',
          );
        }
        if (exception.type == DioExceptionType.connectionTimeout) {
          return AppErrorException(
            'Connection Timeout',
            'no_internet',
          );
        }
        if (exception.type == DioExceptionType.sendTimeout ||
            exception.type == DioExceptionType.receiveTimeout) {
          return AppErrorException(
            'Request Timeout',
            'no_internet',
          );
        }
        // Handle 5xx server errors
        if (statusCode != null && statusCode >= 500 && statusCode < 600) {
          return AppErrorException(
            'Server Error',
            'SERVER_ERROR',
          );
        }
      }
      return AppErrorException('', 'default');
    } catch (errorTransform) {
      developer.log('errorTransform $errorTransform', name: 'BaseApiService');
      throw exception;
    }
  }
}
