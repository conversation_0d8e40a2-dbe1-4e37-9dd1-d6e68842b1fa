import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/folder/widget/folder_view_data.dart';

class CommonFolderItemWidget extends StatelessWidget {
  final FolderViewData viewData;
  final bool isTablet;
  final bool isMultiSelectMode;
  final bool isSelected;
  final VoidCallback? onSelectionTap;
  final VoidCallback? onLongPress;

  const CommonFolderItemWidget({
    required this.viewData,
    required this.isTablet,
    this.isMultiSelectMode = false,
    this.isSelected = false,
    this.onSelectionTap,
    this.onLongPress,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isMultiSelectMode ? onSelectionTap : viewData.onTap,
      onLongPress: onLongPress,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected && isMultiSelectMode
              ? context.colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16.r),
          border: isSelected && isMultiSelectMode
              ? Border.all(
                  color: context.colorScheme.primary.withOpacity(0.3),
                  width: 1,
                )
              : null,
        ),
        child: _buildFolderItemWidget(context),
      ),
    );
  }

  /// Builds a folder item widget with folder information and optional error indicator
  Widget _buildFolderItemWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12.h,
        horizontal: 16.w,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: context.colorScheme.mainBackground,
      ),
      child: Stack(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Checkbox for multi-select mode (matching HomeItemNoteWidget pattern)
              if (isMultiSelectMode)
                Padding(
                  padding: EdgeInsets.only(right: 12.w),
                  child: GestureDetector(
                    onTap: onSelectionTap,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: context.colorScheme.mainBlue,
                          width: 2,
                        ),
                        color: isSelected
                            ? context.colorScheme.mainBlue
                            : Colors.transparent,
                      ),
                      child: isSelected
                          ? SvgPicture.asset(
                              Assets.icons.icCheckBlue,
                              colorFilter: ColorFilter.mode(
                                context.colorScheme.themeWhite,
                                BlendMode.srcIn,
                              ),
                            )
                          : null,
                    ),
                  ),
                ),

              // Folder icon
              _buildFolderIcon(context),
              const SizedBox(width: 12),

              // Folder info
              _buildFolderInfo(context),
            ],
          ),

          // Folder options (hidden in multi-select mode)
          if (!isMultiSelectMode)
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: Center(
                child: _buildFolderOptions(context),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds the folder icon with optional error indicator
  Widget _buildFolderIcon(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(isTablet ? 16 : 16.w),
          width: isTablet ? 64 : 64.w,
          height: isTablet ? 64 : 64.w,
          decoration: BoxDecoration(
            color: context.colorScheme.mainNeutral,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: SvgPicture.asset(viewData.icon),
        ),
        if (viewData.isUnsyncedNote == true)
          Positioned(
            bottom: 0,
            right: 0,
            child: SvgPicture.asset(
              Assets.icons.icFolderError,
              width: 16,
              height: 16,
            ),
          ),
      ],
    );
  }

  /// Builds the folder information section (name and note count)
  Widget _buildFolderInfo(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(right: isMultiSelectMode ? 0 : 24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            CommonText(
              viewData.folderName,
              style: TextStyle(
                fontSize: isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
                color: context.colorScheme.mainPrimary,
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 1,
            ),
            _buildCountsInfo(context),
          ],
        ),
      ),
    );
  }

  /// Builds the counts information (notes count and subfolders count)
  Widget _buildCountsInfo(BuildContext context) {
    final hasNotes = viewData.numberOfNotes > 0;
    final hasSubfolders = viewData.subfoldersCount > 0;

    if (!hasNotes && !hasSubfolders) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: isTablet ? 4 : 4.h),
      child: Row(
        children: [
          if (hasNotes) ...[
            CommonText(
              "${viewData.numberOfNotes} ${S.current.notes}",
              style: TextStyle(
                fontSize: isTablet ? 14 : 12.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainGray.withOpacity(0.6),
              ),
            ),
          ],
          if (hasNotes && hasSubfolders) ...[
            CommonText(
              " • ",
              style: TextStyle(
                fontSize: isTablet ? 14 : 12.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainGray.withOpacity(0.6),
              ),
            ),
          ],
          if (hasSubfolders) ...[
            CommonText(
              "${viewData.subfoldersCount} folders",
              style: TextStyle(
                fontSize: isTablet ? 14 : 12.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainGray.withOpacity(0.6),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Builds the folder options button (if applicable)
  Widget _buildFolderOptions(BuildContext context) {
    final editNameController = TextEditingController();
    if (!viewData.showOptions) {
      return const SizedBox.shrink();
    }

    // Create a temporary FolderModel for the FolderMoreOptions widget
    final tempFolder = FolderModel(
      folderName: viewData.folderName,
      backendId: viewData.id,
    );

    return FolderMoreOptions(
      folder: tempFolder,
      editFolderController: editNameController,
      icon: Assets.icons.icFolderMore,
      isInDetailPage: false,
      onEditFolderConfirm: viewData.onEditFolderConfirm,
      onDeleteConfirm: ([deleteNotes]) => viewData.onDeleteConfirm?.call(deleteNotes),
    );
  }
}
