import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:pull_down_button/pull_down_button.dart';

class ChatHistoryDetail extends StatelessWidget {
  final ChatMessageModel chatMessageModel;
  final NoteModel noteModel;

  const ChatHistoryDetail({
    super.key,
    required this.chatMessageModel,
    required this.noteModel,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: GetIt.instance.get<ChatCubit>(),
      child: Scaffold(
        appBar: AppBarWidget(
          customTitle: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: CommonText(
              chatMessageModel.title.removeNewLines(),
              style: TextStyle(
                color: context.colorScheme.mainPrimary,
                fontSize: context.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w700,
              ),
              maxLines: 1,
            ),
          ),
          isShowLeftButton: true,
          onPressed: () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.ai_chat_saved_chat_close,
            );
            Navigator.pop(context);
          },
          backgroundColor: context.colorScheme.mainBackground,
          actions: [
            PullDownButton(
              routeTheme: PullDownMenuRouteTheme(
                backgroundColor: context.colorScheme.mainNeutral,
                borderRadius: BorderRadius.circular(16.r),
                width: context.isTablet ? 200 : 200.w,
              ),
              itemBuilder: (context) => [
                PullDownMenuItem(
                  title: S.current.copy,
                  itemTheme: PullDownMenuItemTheme(
                    textStyle: TextStyle(
                      color: context.colorScheme.mainPrimary,
                      fontSize: context.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onTap: () {
                    AnalyticsService.logAnalyticsEventNoParam(
                      eventName: EventName.ai_chat_saved_chat_copy,
                    );
                    Clipboard.setData(ClipboardData(
                        text: MyUtils.markdownToPlainText(
                            chatMessageModel.answer)));
                    CommonDialogs.showToast(
                      S.current.copied_to_clipboard,
                    );
                  },
                  iconWidget: SvgPicture.asset(
                    Assets.icons.icCopy,
                    width: 18.w,
                    height: 18.h,
                    colorFilter: ColorFilter.mode(
                      context.colorScheme.mainPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
                PullDownMenuItem(
                  title: S.current.delete,
                  itemTheme: PullDownMenuItemTheme(
                    textStyle: TextStyle(
                      color: Colors.red,
                      fontSize: context.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onTap: () async {
                    AnalyticsService.logAnalyticsEventNoParam(
                      eventName: EventName.ai_chat_saved_chat_delete,
                    );
                    CommonDialogs.buildDeleteDialog(context,
                        title: S.current.delete_this_item,
                        content: S.current.you_will_not_be,
                        headerImageAssetFile: Assets.icons.icMascottDelete,
                        onPressedDeleteButton: ([_]) async {
                      await context.read<ChatCubit>().deleteChatMessage(
                            note: noteModel,
                            chatMessage: chatMessageModel,
                          );
                      if (context.mounted) Navigator.pop(context);
                    }, onPressedCancelButton: () {});
                  },
                  iconWidget: SvgPicture.asset(
                    Assets.icons.icDelete,
                    width: context.isTablet ? 18 : 18.w,
                    height: context.isTablet ? 18 : 18.w,
                    colorFilter: const ColorFilter.mode(
                      Colors.red,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
              buttonBuilder: (context, showMenu) => IconButton(
                onPressed: showMenu,
                icon: SvgPicture.asset(
                  Assets.icons.icMore,
                  width: context.isTablet ? 24 : 24.w,
                  height: context.isTablet ? 24 : 24.w,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Markdown(
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 16.h,
          ),
          data: chatMessageModel.answer,
          styleSheet: MyUtils.getChatMarkdownStyleSheet(
            context,
            context.isTablet ? 16 : 15.sp,
          ),
        ),
      ),
    );
  }
}
