// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'short_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ShortDetailsState {
  ShortsDetailOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  bool get didShareOrDownloadShortsResult => throw _privateConstructorUsedError;
  bool get isWatermarkEnable => throw _privateConstructorUsedError;
  String get selectedUrlShortsBackgrounds => throw _privateConstructorUsedError;
  String get currentPlayingFilePath => throw _privateConstructorUsedError;
  String get audioFilePath => throw _privateConstructorUsedError;
  String get subtitleFilePath => throw _privateConstructorUsedError;
  ProcessingStatus get processingStatus => throw _privateConstructorUsedError;
  ShortQuizOneShotEvent get shortQuizOneShotEvent =>
      throw _privateConstructorUsedError;
  Map<String, String> get mergedVideosMap => throw _privateConstructorUsedError;
  String get quizVideoUrl => throw _privateConstructorUsedError;
  String get progressMessage => throw _privateConstructorUsedError;
  String get selectedQuizVideoUrl => throw _privateConstructorUsedError;
  bool get isQuizControlsVisible => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ShortDetailsStateCopyWith<ShortDetailsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShortDetailsStateCopyWith<$Res> {
  factory $ShortDetailsStateCopyWith(
          ShortDetailsState value, $Res Function(ShortDetailsState) then) =
      _$ShortDetailsStateCopyWithImpl<$Res, ShortDetailsState>;
  @useResult
  $Res call(
      {ShortsDetailOneShotEvent oneShotEvent,
      bool didShareOrDownloadShortsResult,
      bool isWatermarkEnable,
      String selectedUrlShortsBackgrounds,
      String currentPlayingFilePath,
      String audioFilePath,
      String subtitleFilePath,
      ProcessingStatus processingStatus,
      ShortQuizOneShotEvent shortQuizOneShotEvent,
      Map<String, String> mergedVideosMap,
      String quizVideoUrl,
      String progressMessage,
      String selectedQuizVideoUrl,
      bool isQuizControlsVisible});
}

/// @nodoc
class _$ShortDetailsStateCopyWithImpl<$Res, $Val extends ShortDetailsState>
    implements $ShortDetailsStateCopyWith<$Res> {
  _$ShortDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? didShareOrDownloadShortsResult = null,
    Object? isWatermarkEnable = null,
    Object? selectedUrlShortsBackgrounds = null,
    Object? currentPlayingFilePath = null,
    Object? audioFilePath = null,
    Object? subtitleFilePath = null,
    Object? processingStatus = null,
    Object? shortQuizOneShotEvent = null,
    Object? mergedVideosMap = null,
    Object? quizVideoUrl = null,
    Object? progressMessage = null,
    Object? selectedQuizVideoUrl = null,
    Object? isQuizControlsVisible = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as ShortsDetailOneShotEvent,
      didShareOrDownloadShortsResult: null == didShareOrDownloadShortsResult
          ? _value.didShareOrDownloadShortsResult
          : didShareOrDownloadShortsResult // ignore: cast_nullable_to_non_nullable
              as bool,
      isWatermarkEnable: null == isWatermarkEnable
          ? _value.isWatermarkEnable
          : isWatermarkEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedUrlShortsBackgrounds: null == selectedUrlShortsBackgrounds
          ? _value.selectedUrlShortsBackgrounds
          : selectedUrlShortsBackgrounds // ignore: cast_nullable_to_non_nullable
              as String,
      currentPlayingFilePath: null == currentPlayingFilePath
          ? _value.currentPlayingFilePath
          : currentPlayingFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      audioFilePath: null == audioFilePath
          ? _value.audioFilePath
          : audioFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      subtitleFilePath: null == subtitleFilePath
          ? _value.subtitleFilePath
          : subtitleFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      processingStatus: null == processingStatus
          ? _value.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      shortQuizOneShotEvent: null == shortQuizOneShotEvent
          ? _value.shortQuizOneShotEvent
          : shortQuizOneShotEvent // ignore: cast_nullable_to_non_nullable
              as ShortQuizOneShotEvent,
      mergedVideosMap: null == mergedVideosMap
          ? _value.mergedVideosMap
          : mergedVideosMap // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      quizVideoUrl: null == quizVideoUrl
          ? _value.quizVideoUrl
          : quizVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      progressMessage: null == progressMessage
          ? _value.progressMessage
          : progressMessage // ignore: cast_nullable_to_non_nullable
              as String,
      selectedQuizVideoUrl: null == selectedQuizVideoUrl
          ? _value.selectedQuizVideoUrl
          : selectedQuizVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isQuizControlsVisible: null == isQuizControlsVisible
          ? _value.isQuizControlsVisible
          : isQuizControlsVisible // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShortDetailsStateImplCopyWith<$Res>
    implements $ShortDetailsStateCopyWith<$Res> {
  factory _$$ShortDetailsStateImplCopyWith(_$ShortDetailsStateImpl value,
          $Res Function(_$ShortDetailsStateImpl) then) =
      __$$ShortDetailsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ShortsDetailOneShotEvent oneShotEvent,
      bool didShareOrDownloadShortsResult,
      bool isWatermarkEnable,
      String selectedUrlShortsBackgrounds,
      String currentPlayingFilePath,
      String audioFilePath,
      String subtitleFilePath,
      ProcessingStatus processingStatus,
      ShortQuizOneShotEvent shortQuizOneShotEvent,
      Map<String, String> mergedVideosMap,
      String quizVideoUrl,
      String progressMessage,
      String selectedQuizVideoUrl,
      bool isQuizControlsVisible});
}

/// @nodoc
class __$$ShortDetailsStateImplCopyWithImpl<$Res>
    extends _$ShortDetailsStateCopyWithImpl<$Res, _$ShortDetailsStateImpl>
    implements _$$ShortDetailsStateImplCopyWith<$Res> {
  __$$ShortDetailsStateImplCopyWithImpl(_$ShortDetailsStateImpl _value,
      $Res Function(_$ShortDetailsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? didShareOrDownloadShortsResult = null,
    Object? isWatermarkEnable = null,
    Object? selectedUrlShortsBackgrounds = null,
    Object? currentPlayingFilePath = null,
    Object? audioFilePath = null,
    Object? subtitleFilePath = null,
    Object? processingStatus = null,
    Object? shortQuizOneShotEvent = null,
    Object? mergedVideosMap = null,
    Object? quizVideoUrl = null,
    Object? progressMessage = null,
    Object? selectedQuizVideoUrl = null,
    Object? isQuizControlsVisible = null,
  }) {
    return _then(_$ShortDetailsStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as ShortsDetailOneShotEvent,
      didShareOrDownloadShortsResult: null == didShareOrDownloadShortsResult
          ? _value.didShareOrDownloadShortsResult
          : didShareOrDownloadShortsResult // ignore: cast_nullable_to_non_nullable
              as bool,
      isWatermarkEnable: null == isWatermarkEnable
          ? _value.isWatermarkEnable
          : isWatermarkEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedUrlShortsBackgrounds: null == selectedUrlShortsBackgrounds
          ? _value.selectedUrlShortsBackgrounds
          : selectedUrlShortsBackgrounds // ignore: cast_nullable_to_non_nullable
              as String,
      currentPlayingFilePath: null == currentPlayingFilePath
          ? _value.currentPlayingFilePath
          : currentPlayingFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      audioFilePath: null == audioFilePath
          ? _value.audioFilePath
          : audioFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      subtitleFilePath: null == subtitleFilePath
          ? _value.subtitleFilePath
          : subtitleFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      processingStatus: null == processingStatus
          ? _value.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      shortQuizOneShotEvent: null == shortQuizOneShotEvent
          ? _value.shortQuizOneShotEvent
          : shortQuizOneShotEvent // ignore: cast_nullable_to_non_nullable
              as ShortQuizOneShotEvent,
      mergedVideosMap: null == mergedVideosMap
          ? _value._mergedVideosMap
          : mergedVideosMap // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      quizVideoUrl: null == quizVideoUrl
          ? _value.quizVideoUrl
          : quizVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      progressMessage: null == progressMessage
          ? _value.progressMessage
          : progressMessage // ignore: cast_nullable_to_non_nullable
              as String,
      selectedQuizVideoUrl: null == selectedQuizVideoUrl
          ? _value.selectedQuizVideoUrl
          : selectedQuizVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isQuizControlsVisible: null == isQuizControlsVisible
          ? _value.isQuizControlsVisible
          : isQuizControlsVisible // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ShortDetailsStateImpl implements _ShortDetailsState {
  const _$ShortDetailsStateImpl(
      {this.oneShotEvent = ShortsDetailOneShotEvent.none,
      this.didShareOrDownloadShortsResult = false,
      this.isWatermarkEnable = false,
      this.selectedUrlShortsBackgrounds = '',
      this.currentPlayingFilePath = '',
      this.audioFilePath = '',
      this.subtitleFilePath = '',
      this.processingStatus = ProcessingStatus.initial,
      this.shortQuizOneShotEvent = ShortQuizOneShotEvent.initial,
      final Map<String, String> mergedVideosMap = const <String, String>{},
      this.quizVideoUrl = '',
      this.progressMessage = '',
      this.selectedQuizVideoUrl = '',
      this.isQuizControlsVisible = false})
      : _mergedVideosMap = mergedVideosMap;

  @override
  @JsonKey()
  final ShortsDetailOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final bool didShareOrDownloadShortsResult;
  @override
  @JsonKey()
  final bool isWatermarkEnable;
  @override
  @JsonKey()
  final String selectedUrlShortsBackgrounds;
  @override
  @JsonKey()
  final String currentPlayingFilePath;
  @override
  @JsonKey()
  final String audioFilePath;
  @override
  @JsonKey()
  final String subtitleFilePath;
  @override
  @JsonKey()
  final ProcessingStatus processingStatus;
  @override
  @JsonKey()
  final ShortQuizOneShotEvent shortQuizOneShotEvent;
  final Map<String, String> _mergedVideosMap;
  @override
  @JsonKey()
  Map<String, String> get mergedVideosMap {
    if (_mergedVideosMap is EqualUnmodifiableMapView) return _mergedVideosMap;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_mergedVideosMap);
  }

  @override
  @JsonKey()
  final String quizVideoUrl;
  @override
  @JsonKey()
  final String progressMessage;
  @override
  @JsonKey()
  final String selectedQuizVideoUrl;
  @override
  @JsonKey()
  final bool isQuizControlsVisible;

  @override
  String toString() {
    return 'ShortDetailsState(oneShotEvent: $oneShotEvent, didShareOrDownloadShortsResult: $didShareOrDownloadShortsResult, isWatermarkEnable: $isWatermarkEnable, selectedUrlShortsBackgrounds: $selectedUrlShortsBackgrounds, currentPlayingFilePath: $currentPlayingFilePath, audioFilePath: $audioFilePath, subtitleFilePath: $subtitleFilePath, processingStatus: $processingStatus, shortQuizOneShotEvent: $shortQuizOneShotEvent, mergedVideosMap: $mergedVideosMap, quizVideoUrl: $quizVideoUrl, progressMessage: $progressMessage, selectedQuizVideoUrl: $selectedQuizVideoUrl, isQuizControlsVisible: $isQuizControlsVisible)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShortDetailsStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.didShareOrDownloadShortsResult,
                    didShareOrDownloadShortsResult) ||
                other.didShareOrDownloadShortsResult ==
                    didShareOrDownloadShortsResult) &&
            (identical(other.isWatermarkEnable, isWatermarkEnable) ||
                other.isWatermarkEnable == isWatermarkEnable) &&
            (identical(other.selectedUrlShortsBackgrounds,
                    selectedUrlShortsBackgrounds) ||
                other.selectedUrlShortsBackgrounds ==
                    selectedUrlShortsBackgrounds) &&
            (identical(other.currentPlayingFilePath, currentPlayingFilePath) ||
                other.currentPlayingFilePath == currentPlayingFilePath) &&
            (identical(other.audioFilePath, audioFilePath) ||
                other.audioFilePath == audioFilePath) &&
            (identical(other.subtitleFilePath, subtitleFilePath) ||
                other.subtitleFilePath == subtitleFilePath) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.shortQuizOneShotEvent, shortQuizOneShotEvent) ||
                other.shortQuizOneShotEvent == shortQuizOneShotEvent) &&
            const DeepCollectionEquality()
                .equals(other._mergedVideosMap, _mergedVideosMap) &&
            (identical(other.quizVideoUrl, quizVideoUrl) ||
                other.quizVideoUrl == quizVideoUrl) &&
            (identical(other.progressMessage, progressMessage) ||
                other.progressMessage == progressMessage) &&
            (identical(other.selectedQuizVideoUrl, selectedQuizVideoUrl) ||
                other.selectedQuizVideoUrl == selectedQuizVideoUrl) &&
            (identical(other.isQuizControlsVisible, isQuizControlsVisible) ||
                other.isQuizControlsVisible == isQuizControlsVisible));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      didShareOrDownloadShortsResult,
      isWatermarkEnable,
      selectedUrlShortsBackgrounds,
      currentPlayingFilePath,
      audioFilePath,
      subtitleFilePath,
      processingStatus,
      shortQuizOneShotEvent,
      const DeepCollectionEquality().hash(_mergedVideosMap),
      quizVideoUrl,
      progressMessage,
      selectedQuizVideoUrl,
      isQuizControlsVisible);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShortDetailsStateImplCopyWith<_$ShortDetailsStateImpl> get copyWith =>
      __$$ShortDetailsStateImplCopyWithImpl<_$ShortDetailsStateImpl>(
          this, _$identity);
}

abstract class _ShortDetailsState implements ShortDetailsState {
  const factory _ShortDetailsState(
      {final ShortsDetailOneShotEvent oneShotEvent,
      final bool didShareOrDownloadShortsResult,
      final bool isWatermarkEnable,
      final String selectedUrlShortsBackgrounds,
      final String currentPlayingFilePath,
      final String audioFilePath,
      final String subtitleFilePath,
      final ProcessingStatus processingStatus,
      final ShortQuizOneShotEvent shortQuizOneShotEvent,
      final Map<String, String> mergedVideosMap,
      final String quizVideoUrl,
      final String progressMessage,
      final String selectedQuizVideoUrl,
      final bool isQuizControlsVisible}) = _$ShortDetailsStateImpl;

  @override
  ShortsDetailOneShotEvent get oneShotEvent;
  @override
  bool get didShareOrDownloadShortsResult;
  @override
  bool get isWatermarkEnable;
  @override
  String get selectedUrlShortsBackgrounds;
  @override
  String get currentPlayingFilePath;
  @override
  String get audioFilePath;
  @override
  String get subtitleFilePath;
  @override
  ProcessingStatus get processingStatus;
  @override
  ShortQuizOneShotEvent get shortQuizOneShotEvent;
  @override
  Map<String, String> get mergedVideosMap;
  @override
  String get quizVideoUrl;
  @override
  String get progressMessage;
  @override
  String get selectedQuizVideoUrl;
  @override
  bool get isQuizControlsVisible;
  @override
  @JsonKey(ignore: true)
  _$$ShortDetailsStateImplCopyWith<_$ShortDetailsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
