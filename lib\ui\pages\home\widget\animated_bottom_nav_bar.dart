import 'package:flutter/material.dart';

class AnimatedBottomNavBar extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final VoidCallback? onAnimationComplete;

  const AnimatedBottomNavBar({
    Key? key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.onAnimationComplete,
  }) : super(key: key);

  @override
  State<AnimatedBottomNavBar> createState() => AnimatedBottomNavBarState();
}

class AnimatedBottomNavBarState extends State<AnimatedBottomNavBar>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Hidden position
      end: Offset.zero,          // Visible position
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.curve,
    ));

    // Start with visible state
    _animationController.forward();

    // Listen for animation completion
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed || 
          status == AnimationStatus.dismissed) {
        widget.onAnimationComplete?.call();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Public methods to control animation
  void show() {
    if (!_isVisible) {
      _animationController.forward();
      setState(() {
        _isVisible = true;
      });
    }
  }

  void hide() {
    if (_isVisible) {
      _animationController.reverse();
      setState(() {
        _isVisible = false;
      });
    }
  }

  void toggle() {
    if (_isVisible) {
      hide();
    } else {
      show();
    }
  }

  bool get isVisible => _isVisible;

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: widget.child,
    );
  }
}
