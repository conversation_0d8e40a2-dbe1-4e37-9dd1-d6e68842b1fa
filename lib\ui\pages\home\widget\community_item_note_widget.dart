import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';

/// A widget that displays a community note item in the home screen.
/// This widget is simplified and doesn't include overflow menu functionality.
class CommunityItemNoteWidget extends StatelessWidget {
  final NoteModel noteModel;
  final Function()? onTap;

  const CommunityItemNoteWidget({
    super.key,
    required this.noteModel,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return noteModel.noteStatus == NoteStatus.loading
        ? _buildCreatingNoteWidget(noteModel, context)
        : _buildNoteWidget(context);
  }

  Widget _buildCreatingNoteWidget(NoteModel noteModel, BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: context.isLandscape ? 12 : 12.h,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: const LinearGradient(
            colors: AppColors.gradientCTABlue,
            begin: Alignment.centerRight,
            end: Alignment.centerLeft,
          ),
        ),
        child: Row(
          children: [
            Lottie.asset(
              Assets.videos.shortsCreating,
              width: context.isTablet ? 36 : 40.w,
              height: context.isTablet ? 36 : 40.w,
            ),
            SizedBox(width: context.isTablet ? 8 : 16.w),
            AnimatedTextEffect(
              text: noteModel.currentStep.stepName,
              style: TextStyle(
                color: AppColors.white,
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoteWidget(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: context.colorScheme.mainNeutral,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: DirectionalText(
                    text: noteModel.title,
                    detectFromContent: true,
                    style: TextStyle(
                      color: context.colorScheme.mainPrimary,
                      fontWeight: FontWeight.w500,
                      fontSize: context.isTablet ? 16 : 14.sp,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            context.isTablet
                ? const SizedBox(height: 4)
                : SizedBox(height: 4.h),
            CommonText(
              buildDateTimeAndDurationText(noteModel),
              style: TextStyle(
                fontSize: context.isTablet ? 14 : 12.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainGray.withOpacity(0.6),
              ),
            ),
            context.isTablet
                ? const SizedBox(height: 8)
                : SizedBox(height: 8.h),
            Row(
              children: [
                Container(
                  height: 24.h,
                  padding: EdgeInsets.symmetric(horizontal: 8.w),
                  decoration: BoxDecoration(
                    color: getColorByNoteTypeContainer(context, noteModel),
                    borderRadius: BorderRadius.circular(100.r),
                  ),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        height: 16.h,
                        width: 16.w,
                        getIconByNoteTypeContainer(noteModel),
                        colorFilter: ColorFilter.mode(
                          getColorByNoteType(context, noteModel),
                          BlendMode.srcIn,
                        ),
                      ),
                      context.isTablet
                          ? const SizedBox(width: 4)
                          : AppConstants.kSpacingItemW4,
                      Flexible(
                        fit: FlexFit.loose,
                        child: Container(
                          constraints: BoxConstraints(maxWidth: 120.w),
                          child: CommonText(
                            buildTextTypeOfNote(noteModel),
                            style: TextStyle(
                              height: 1,
                              fontWeight: FontWeight.w400,
                              fontSize: context.isTablet ? 14 : 12.sp,
                              color: getColorByNoteType(context, noteModel),
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
