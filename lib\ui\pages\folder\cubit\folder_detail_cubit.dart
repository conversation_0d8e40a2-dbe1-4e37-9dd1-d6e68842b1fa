import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/base/base.dart';
import 'package:note_x/services/create_note_api_service.dart';
import 'package:note_x/services/hive/hive_folder_service.dart';
import 'package:note_x/services/hive/hive_service.dart';

import '../../../../services/folder_api_service.dart';
import '../../../../services/folder_service.dart';
import 'folder_state.dart';

class FolderDetailCubit extends BaseCubit<FolderState> {
  FolderDetailCubit(super.initialState);

  final _folderService = GetIt.instance.get<FolderService>();
  final noteApiService = GetIt.instance.get<CreateNoteApiServiceImpl>();
  late List<FolderModel> _folders;
  late String _userId;
  List<NoteModel> _unsyncedNotes = [];

  List<NoteModel> get unsyncedNotes => _unsyncedNotes;

  String get userId => _userId;

  List<FolderModel> get folders => _folders;

  Future<void> initData(FolderModel folderModel) async {
    _userId = appCubit.getAppUser().id;
    await _folderService.getFolderDetail(folderModel.id);
  }

  Future<void> createSubFolder(String name, String parentFolderId) async {
    emit(state.copyWith(oneShotEvent: FolderOneShotEvent.loading));
    await FolderService()
        .createSubfolder(name: name, parentFolderId: parentFolderId);
    emit(state.copyWith(oneShotEvent: FolderOneShotEvent.success));
  }

  Future<void> editFolderName(String newName, FolderModel folder) async {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_edit_folder_confirm,
    );
    runCubitCatching(action: () async {
      final notesInFolder = await HiveFolderService.getNotesInFolder(
        folderBackendId: folder.backendId,
      );
      await _folderService.editFolderName(
          folderId: folder.id, newName: newName);
      // await HiveFolderService.renameFolderById(
      //   folderLocalId: folder.id,
      //   newName: newName,
      // );
      // for (var note in notesInFolder) {
      //   await HiveService().createOrUpdateNote(
      //     note.id,
      //     note.copyWith(
      //       folderName: newName,
      //     ),
      //   );
      // }
    });
  }

  Future<void> deleteFolder({
    required bool deleteNotes,
    required FolderModel folderNote,
  }) async {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.folder_detail_delete_folder_confirm,
    );
    emit(state.copyWith(oneShotEvent: FolderOneShotEvent.loading));
    runCubitCatching(action: () async {
      final notesInFolder = await HiveFolderService.getNotesInFolder(
        folderBackendId: folderNote.backendId,
      );
      await _folderService.deleteFolder(folderId: folderNote.backendId);

      await HiveFolderService.deleteFolder(
        folderLocalId: folderNote.id,
        deleteNotes: deleteNotes,
      );
      for (var note in notesInFolder) {
        if (deleteNotes) {
          await _safelyDeleteNote(note);
        } else {
          await _safelyUpdateNote(note);
        }
      }
      emit(state.copyWith(oneShotEvent: FolderOneShotEvent.success));
    });
  }

  Future<void> _safelyDeleteNote(NoteModel note) async {
    try {
      await noteApiService.deleteNote(noteId: note.backendNoteId);
      await HiveService().deleteNoteById(note.id);
    } catch (e) {
      debugPrint('Error deleting note: $e');
    }
  }

  Future<void> _safelyUpdateNote(NoteModel note) async {
    try {
      await noteApiService.updateNote(
        backendNoteId: note.backendNoteId,
        folderId: '',
      );
      await HiveService().createOrUpdateNote(
        note.id,
        note.copyWith(
          folderId: '',
          folderName: '',
        ),
      );
    } catch (e) {
      debugPrint('Error updating note: $e');
    }
  }

  Future<void> getNoteFail() async {
    _userId = appCubit.getAppUser().id;
    final notes = await HiveService().getNotesFailed(_userId);
    _unsyncedNotes = notes;
  }

  void setInitialFolders(List<FolderModel> folders) {
    emit(state.copyWith(folders: folders));
  }

  Future<void> moveFolderToFolder(List<String> folderId,
      List<NoteModel> notesToBeMoved, String targetFolderId) async {
    emit(state.copyWith(oneShotEvent: FolderOneShotEvent.loading));
    //TODO: logic congnm
    emit(state.copyWith(oneShotEvent: FolderOneShotEvent.success));
  }
}
